"""
AI配音核心处理模块
使用edge-tts进行文本转语音，使用OpenAI Whisper生成字幕
"""

import os
import re
import asyncio
import edge_tts
from datetime import timedelta
import tempfile
import subprocess
import threading
import time
import shutil
import json
from datetime import datetime
import wave
import math
import platform
import psutil
from concurrent.futures import ThreadPoolExecutor, as_completed, TimeoutError
from .config import config_manager
import numpy as np
import sys
import signal
import functools

# 添加common模块路径
sys.path.append(os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(__file__)))))
from common.logger import logger


def timeout_handler(timeout_seconds=120):
    """
    超时装饰器，用于防止操作长时间卡住

    参数:
        timeout_seconds (int): 超时时间（秒），默认120秒
    """
    def decorator(func):
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            # 对于异步函数，使用asyncio.wait_for
            if asyncio.iscoroutinefunction(func):
                async def async_wrapper():
                    try:
                        return await asyncio.wait_for(func(*args, **kwargs), timeout=timeout_seconds)
                    except asyncio.TimeoutError:
                        raise TimeoutError(f"操作超时 ({timeout_seconds}秒): {func.__name__}")
                return async_wrapper()
            else:
                # 对于同步函数，使用线程池和超时
                with ThreadPoolExecutor(max_workers=1) as executor:
                    future = executor.submit(func, *args, **kwargs)
                    try:
                        return future.result(timeout=timeout_seconds)
                    except TimeoutError:
                        raise TimeoutError(f"操作超时 ({timeout_seconds}秒): {func.__name__}")
        return wrapper
    return decorator


class OperationTimeoutError(Exception):
    """操作超时异常"""
    pass

# 在导入pydub之前设置FFmpeg路径
def _setup_ffmpeg_before_import():
    """在导入pydub之前设置FFmpeg路径"""
    try:
        # 获取保存的FFmpeg路径
        ffmpeg_path = config_manager.get_ffmpeg_path()
        if ffmpeg_path and os.path.exists(ffmpeg_path):
            # 处理不同的路径格式
            ffmpeg_exe = None
            ffprobe_exe = None
            bin_dir = None

            # 情况1: 如果路径已经是bin目录
            if ffmpeg_path.endswith("bin"):
                bin_dir = ffmpeg_path
                ffmpeg_exe = os.path.join(ffmpeg_path, "ffmpeg.exe")
                ffprobe_exe = os.path.join(ffmpeg_path, "ffprobe.exe")
            else:
                # 情况2: 如果路径是父目录，需要加上bin
                bin_dir = os.path.join(ffmpeg_path, "bin")
                if os.path.exists(bin_dir):
                    ffmpeg_exe = os.path.join(bin_dir, "ffmpeg.exe")
                    ffprobe_exe = os.path.join(bin_dir, "ffprobe.exe")

            if ffmpeg_exe and ffprobe_exe and os.path.exists(ffmpeg_exe) and os.path.exists(ffprobe_exe):
                # 设置环境变量，让pydub能找到FFmpeg
                os.environ['FFMPEG_BINARY'] = ffmpeg_exe
                os.environ['FFPROBE_BINARY'] = ffprobe_exe

                # 设置pydub的subprocess参数，隐藏窗口
                if platform.system() == "Windows":
                    os.environ['PYDUB_SUBPROCESS_CREATION_FLAGS'] = str(subprocess.CREATE_NO_WINDOW)

                # 也添加到PATH中
                current_path = os.environ.get('PATH', '')
                if bin_dir not in current_path:
                    os.environ['PATH'] = bin_dir + os.pathsep + current_path

                print(f"pydub FFmpeg路径设置: {ffmpeg_exe}")
                print(f"pydub FFprobe路径设置: {ffprobe_exe}")
                return True
    except Exception as e:
        print(f"设置pydub FFmpeg路径失败: {e}")
        pass  # 忽略设置错误
    return False

# 设置FFmpeg路径
_setup_ffmpeg_before_import()

# 现在导入pydub
from pydub import AudioSegment
from pydub.utils import which

# 导入后立即设置pydub的路径属性
def _configure_pydub_after_import():
    """导入pydub后立即配置路径"""
    try:
        ffmpeg_path = config_manager.get_ffmpeg_path()
        if ffmpeg_path and os.path.exists(ffmpeg_path):
            bin_dir = os.path.join(ffmpeg_path, "bin")
            if os.path.exists(bin_dir):
                ffmpeg_exe = os.path.join(bin_dir, "ffmpeg.exe")
                ffprobe_exe = os.path.join(bin_dir, "ffprobe.exe")

                if os.path.exists(ffmpeg_exe) and os.path.exists(ffprobe_exe):
                    # 直接设置pydub的路径属性
                    AudioSegment.converter = ffmpeg_exe
                    AudioSegment.ffmpeg = ffmpeg_exe
                    AudioSegment.ffprobe = ffprobe_exe

                    # 同时设置pydub.utils模块的路径
                    import pydub.utils
                    pydub.utils.which("ffmpeg")  # 触发路径检测
                    pydub.utils.which("ffprobe")  # 触发路径检测

                    print(f"pydub AudioSegment路径配置完成")
    except Exception as e:
        print(f"配置pydub路径失败: {e}")

# 配置pydub
_configure_pydub_after_import()

# 强力设置pydub路径，解决FFprobe警告
def _force_set_pydub_paths():
    """强力设置pydub的FFmpeg和FFprobe路径"""
    try:
        import pydub.utils

        # 获取FFmpeg路径
        ffmpeg_path = config_manager.get_ffmpeg_path()
        if ffmpeg_path and os.path.exists(ffmpeg_path):
            # 处理不同的路径格式
            ffmpeg_exe = None
            ffprobe_exe = None

            # 情况1: 如果路径已经是bin目录
            if ffmpeg_path.endswith("bin"):
                ffmpeg_exe = os.path.join(ffmpeg_path, "ffmpeg.exe")
                ffprobe_exe = os.path.join(ffmpeg_path, "ffprobe.exe")
            else:
                # 情况2: 如果路径是父目录，需要加上bin
                bin_dir = os.path.join(ffmpeg_path, "bin")
                if os.path.exists(bin_dir):
                    ffmpeg_exe = os.path.join(bin_dir, "ffmpeg.exe")
                    ffprobe_exe = os.path.join(bin_dir, "ffprobe.exe")

            if ffmpeg_exe and ffprobe_exe and os.path.exists(ffmpeg_exe) and os.path.exists(ffprobe_exe):
                # 方法1: 直接替换pydub.utils中的which函数
                original_which = pydub.utils.which

                def patched_which(program):
                    if program == "ffmpeg":
                        return ffmpeg_exe
                    elif program == "ffprobe":
                        return ffprobe_exe
                    else:
                        return original_which(program)

                pydub.utils.which = patched_which

                # 方法2: 设置AudioSegment的路径属性
                AudioSegment.converter = ffmpeg_exe
                AudioSegment.ffmpeg = ffmpeg_exe
                AudioSegment.ffprobe = ffprobe_exe

                # 方法3: 强制设置环境变量
                os.environ['FFMPEG_BINARY'] = ffmpeg_exe
                os.environ['FFPROBE_BINARY'] = ffprobe_exe

                print(f"强力设置pydub路径完成: FFmpeg={ffmpeg_exe}, FFprobe={ffprobe_exe}")
                return True
    except Exception as e:
        print(f"强力设置pydub路径失败: {e}")
    return False

# 应用强力路径设置
_force_set_pydub_paths()

# 补丁pydub的subprocess调用，确保隐藏窗口
def _patch_pydub_subprocess():
    """补丁pydub的subprocess调用，确保在Windows下隐藏窗口"""
    try:
        import pydub.utils
        original_popen = getattr(pydub.utils, '_original_popen', None)
        if original_popen is None:
            # 保存原始的Popen，避免重复补丁
            original_popen = pydub.utils.Popen
            pydub.utils._original_popen = original_popen

        def patched_popen(*args, **kwargs):
            # 在Windows下添加CREATE_NO_WINDOW标志
            if platform.system() == "Windows":
                if 'creationflags' not in kwargs:
                    kwargs['creationflags'] = subprocess.CREATE_NO_WINDOW
                else:
                    kwargs['creationflags'] |= subprocess.CREATE_NO_WINDOW
            return original_popen(*args, **kwargs)

        # 替换pydub的Popen
        pydub.utils.Popen = patched_popen
        print("pydub subprocess补丁应用成功")

    except Exception as e:
        print(f"pydub subprocess补丁应用失败: {e}")

# 应用pydub补丁
_patch_pydub_subprocess()

# 额外的pydub补丁：直接修改AudioSegment的export方法
def _patch_audiosegment_export():
    """补丁AudioSegment的export方法，确保隐藏窗口"""
    try:
        original_export = AudioSegment.export

        def patched_export(self, out_f=None, format="mp3", codec=None, bitrate=None, parameters=None, tags=None, id3v2_version="4", cover=None):
            # 确保在Windows下使用CREATE_NO_WINDOW
            if platform.system() == "Windows":
                # 临时设置环境变量
                old_creation_flags = os.environ.get('PYDUB_SUBPROCESS_CREATION_FLAGS')
                os.environ['PYDUB_SUBPROCESS_CREATION_FLAGS'] = str(subprocess.CREATE_NO_WINDOW)

                try:
                    result = original_export(self, out_f, format, codec, bitrate, parameters, tags, id3v2_version, cover)
                finally:
                    # 恢复原来的环境变量
                    if old_creation_flags is not None:
                        os.environ['PYDUB_SUBPROCESS_CREATION_FLAGS'] = old_creation_flags
                    else:
                        os.environ.pop('PYDUB_SUBPROCESS_CREATION_FLAGS', None)

                return result
            else:
                return original_export(self, out_f, format, codec, bitrate, parameters, tags, id3v2_version, cover)

        AudioSegment.export = patched_export
        print("AudioSegment.export补丁应用成功")

    except Exception as e:
        print(f"AudioSegment.export补丁应用失败: {e}")

# 应用AudioSegment补丁
_patch_audiosegment_export()

# 最强力的补丁：直接修改pydub模块中的subprocess调用
def _patch_pydub_all_subprocess():
    """全面补丁pydub中的所有subprocess调用"""
    try:
        import pydub.utils
        import pydub.audio_segment

        # 保存原始函数
        if not hasattr(pydub.utils, '_original_subprocess_call'):
            pydub.utils._original_subprocess_call = subprocess.call
            pydub.utils._original_subprocess_check_call = subprocess.check_call
            pydub.utils._original_subprocess_check_output = subprocess.check_output
            pydub.utils._original_subprocess_run = subprocess.run
            pydub.utils._original_subprocess_popen = subprocess.Popen

        def patched_call(*args, **kwargs):
            if platform.system() == "Windows" and 'creationflags' not in kwargs:
                kwargs['creationflags'] = subprocess.CREATE_NO_WINDOW
            return pydub.utils._original_subprocess_call(*args, **kwargs)

        def patched_check_call(*args, **kwargs):
            if platform.system() == "Windows" and 'creationflags' not in kwargs:
                kwargs['creationflags'] = subprocess.CREATE_NO_WINDOW
            return pydub.utils._original_subprocess_check_call(*args, **kwargs)

        def patched_check_output(*args, **kwargs):
            if platform.system() == "Windows" and 'creationflags' not in kwargs:
                kwargs['creationflags'] = subprocess.CREATE_NO_WINDOW
            return pydub.utils._original_subprocess_check_output(*args, **kwargs)

        def patched_run(*args, **kwargs):
            if platform.system() == "Windows" and 'creationflags' not in kwargs:
                kwargs['creationflags'] = subprocess.CREATE_NO_WINDOW
            return pydub.utils._original_subprocess_run(*args, **kwargs)

        def patched_popen(*args, **kwargs):
            if platform.system() == "Windows" and 'creationflags' not in kwargs:
                kwargs['creationflags'] = subprocess.CREATE_NO_WINDOW
            return pydub.utils._original_subprocess_popen(*args, **kwargs)

        # 替换pydub模块中的subprocess函数
        subprocess.call = patched_call
        subprocess.check_call = patched_check_call
        subprocess.check_output = patched_check_output
        subprocess.run = patched_run
        subprocess.Popen = patched_popen

        print("pydub全面subprocess补丁应用成功")

    except Exception as e:
        print(f"pydub全面subprocess补丁应用失败: {e}")

# 应用全面补丁
_patch_pydub_all_subprocess()

# FFmpeg进程管理
class FFmpegProcessManager:
    """FFmpeg进程管理器，用于跟踪和清理FFmpeg进程"""

    def __init__(self):
        self.active_processes = set()  # 活跃的进程PID
        self.process_lock = threading.Lock()

    def register_process(self, process):
        """注册一个FFmpeg进程"""
        with self.process_lock:
            if hasattr(process, 'pid') and process.pid:
                self.active_processes.add(process.pid)
                print(f"注册FFmpeg进程: PID {process.pid}")

    def unregister_process(self, process):
        """注销一个FFmpeg进程"""
        with self.process_lock:
            if hasattr(process, 'pid') and process.pid:
                self.active_processes.discard(process.pid)
                print(f"注销FFmpeg进程: PID {process.pid}")

    def kill_all_ffmpeg_processes(self):
        """终止本应用启动的FFmpeg进程"""
        killed_count = 0

        try:
            # 只终止我们跟踪的进程（本应用启动的进程）
            with self.process_lock:
                for pid in list(self.active_processes):
                    try:
                        if psutil.pid_exists(pid):
                            process = psutil.Process(pid)
                            if process.is_running():
                                # 验证这确实是我们启动的FFmpeg进程
                                proc_name = process.name().lower()
                                if 'ffmpeg' in proc_name or 'ffprobe' in proc_name:
                                    process.terminate()
                                    killed_count += 1
                                    print(f"终止FFmpeg进程: {proc_name} PID {pid}")
                                else:
                                    print(f"跳过非FFmpeg进程: {proc_name} PID {pid}")
                    except (psutil.NoSuchProcess, psutil.AccessDenied):
                        pass
                self.active_processes.clear()

            # 等待进程正常终止
            time.sleep(0.5)

            # 检查是否还有未终止的进程，如果有则强制终止
            remaining_pids = []
            with self.process_lock:
                # 重新检查我们记录的进程
                for pid in list(self.active_processes):
                    try:
                        if psutil.pid_exists(pid):
                            process = psutil.Process(pid)
                            if process.is_running():
                                proc_name = process.name().lower()
                                if 'ffmpeg' in proc_name or 'ffprobe' in proc_name:
                                    remaining_pids.append((pid, process))
                    except (psutil.NoSuchProcess, psutil.AccessDenied):
                        pass

            # 强制终止仍在运行的进程
            for pid, process in remaining_pids:
                try:
                    process.kill()
                    killed_count += 1
                    print(f"强制终止FFmpeg进程: PID {pid}")
                except (psutil.NoSuchProcess, psutil.AccessDenied):
                    pass

            if killed_count > 0:
                print(f"总共终止了 {killed_count} 个FFmpeg进程")
            else:
                print("没有发现需要终止的FFmpeg进程")

        except Exception as e:
            print(f"终止FFmpeg进程时出错: {e}")

    def cleanup_zombie_processes(self):
        """清理僵尸进程"""
        try:
            with self.process_lock:
                # 清理已经不存在的PID
                existing_pids = set()
                for pid in list(self.active_processes):
                    if psutil.pid_exists(pid):
                        existing_pids.add(pid)
                    else:
                        print(f"清理不存在的进程PID: {pid}")
                self.active_processes = existing_pids
        except Exception as e:
            print(f"清理僵尸进程时出错: {e}")

# 全局FFmpeg进程管理器
ffmpeg_manager = FFmpegProcessManager()

def get_ffmpeg_manager():
    """获取FFmpeg进程管理器（供外部调用）"""
    return ffmpeg_manager

class VoiceTTSProcessor:
    """AI配音处理核心类"""
    
    def __init__(self):
        # 设置事件循环策略，避免Windows下的ProactorEventLoop问题
        self._setup_event_loop_policy()

        # FFmpeg路径设置
        self.ffmpeg_path = None

        # 本地语音识别设置
        self.use_local_stt = True

        # 加载保存的FFmpeg路径
        saved_ffmpeg_path = config_manager.get_ffmpeg_path()
        if saved_ffmpeg_path:
            self.set_ffmpeg_path(saved_ffmpeg_path)
            # 立即确保FFmpeg路径设置正确
            self.ensure_ffmpeg_path()

        # 可用的语音列表（动态获取）
        self.voices = {}
        self.voices_loaded = False



        # 分段长度限制（字符数）- 从配置加载
        self.max_segment_length = config_manager.get_segment_length()

        # 处理线程数
        self.thread_count = config_manager.get_thread_count()

        # 重试设置
        self.retry_count = config_manager.get_retry_count()
        self.retry_interval = config_manager.get_retry_interval()

        # 停顿移除设置已移除

        # 音频质量增强设置
        self.enhance_quality = config_manager.get_enhance_quality()
        self.enhance_clarity = config_manager.get_enhance_clarity()
        self.normalize_volume = config_manager.get_normalize_volume()

        # 控制标志
        self.should_stop = False
        self.is_paused = False

        # 跟踪临时文件和目录，用于清理
        self.temp_files = set()
        self.temp_dirs = set()
        self._temp_lock = threading.Lock()

    def _setup_event_loop_policy(self):
        """设置事件循环策略，避免Windows下的多线程问题"""
        try:
            import asyncio
            import platform

            # 在Windows上使用SelectorEventLoop而不是ProactorEventLoop
            # 这可以避免多线程环境中的"Event loop is closed"错误
            if platform.system() == "Windows":
                if hasattr(asyncio, 'WindowsSelectorEventLoopPolicy'):
                    policy = asyncio.WindowsSelectorEventLoopPolicy()
                    asyncio.set_event_loop_policy(policy)
                    logger.info("已设置Windows SelectorEventLoop策略")

        except Exception as e:
            logger.warning(f"设置事件循环策略失败: {str(e)}")
            # 不抛出异常，继续使用默认策略

    def convert_rate(self, rate_value):
        """
        将语速滑块值转换为SSML格式

        参数:
            rate_value (int): 语速值 (-50 到 +50)

        返回:
            str: SSML格式的语速值
        """
        if rate_value >= 0:
            return f"+{rate_value}%"
        else:
            return f"{rate_value}%"

    def convert_pitch(self, pitch_value):
        """
        将音调滑块值转换为SSML格式

        参数:
            pitch_value (int): 音调值 (-10 到 +10)

        返回:
            str: SSML格式的音调值
        """
        if pitch_value >= 0:
            return f"+{pitch_value * 5}Hz"  # 每个单位对应5Hz
        else:
            return f"{pitch_value * 5}Hz"

    def convert_volume(self, volume_value):
        """
        将音量滑块值转换为SSML格式

        参数:
            volume_value (int): 音量值 (0 到 300)

        返回:
            str: SSML格式的音量值
        """
        # 将0-300转换为相对百分比，50为基准(+0%)
        # 但SSML最大只支持+50%，所以超过100的部分需要后处理
        relative_volume = min(volume_value - 50, 50)  # SSML最大+50%
        if relative_volume >= 0:
            return f"+{relative_volume}%"
        else:
            return f"{relative_volume}%"

    def set_ffmpeg_path(self, ffmpeg_dir):
        """
        设置FFmpeg路径

        参数:
            ffmpeg_dir (str): FFmpeg目录路径（包含bin文件夹）
        """
        if ffmpeg_dir and os.path.exists(ffmpeg_dir):
            # 检查bin目录
            bin_dir = os.path.join(ffmpeg_dir, "bin")
            if os.path.exists(bin_dir):
                ffmpeg_exe = os.path.join(bin_dir, "ffmpeg.exe")
                ffprobe_exe = os.path.join(bin_dir, "ffprobe.exe")
                if os.path.exists(ffmpeg_exe) and os.path.exists(ffprobe_exe):
                    self.ffmpeg_path = ffmpeg_exe
                    # 设置pydub使用的ffmpeg路径
                    AudioSegment.converter = ffmpeg_exe
                    AudioSegment.ffmpeg = ffmpeg_exe
                    AudioSegment.ffprobe = ffprobe_exe

                    # 设置环境变量
                    os.environ['FFMPEG_BINARY'] = ffmpeg_exe
                    os.environ['FFPROBE_BINARY'] = ffprobe_exe

                    # 保存到配置文件
                    config_manager.set_ffmpeg_path(ffmpeg_dir)
                    print(f"FFmpeg路径设置成功: {ffmpeg_exe}")
                    print(f"FFprobe路径设置成功: {ffprobe_exe}")
                    return True
        return False

    def is_ffmpeg_available(self):
        """检查FFmpeg是否可用"""
        if self.ffmpeg_path and os.path.exists(self.ffmpeg_path):
            return True

        # 检查系统PATH中是否有ffmpeg
        try:
            import platform
            if platform.system() == "Windows":
                result = subprocess.run(['ffmpeg', '-version'],
                                      capture_output=True, text=True, timeout=5,
                                      creationflags=subprocess.CREATE_NO_WINDOW)
            else:
                result = subprocess.run(['ffmpeg', '-version'],
                                      capture_output=True, text=True, timeout=5)
            return result.returncode == 0
        except:
            return False

    def ensure_ffmpeg_path(self):
        """确保FFmpeg路径正确设置到pydub"""
        if self.ffmpeg_path and os.path.exists(self.ffmpeg_path):
            # 设置pydub的FFmpeg路径
            AudioSegment.converter = self.ffmpeg_path
            AudioSegment.ffmpeg = self.ffmpeg_path
            ffprobe_path = os.path.join(os.path.dirname(self.ffmpeg_path), "ffprobe.exe")
            if os.path.exists(ffprobe_path):
                AudioSegment.ffprobe = ffprobe_path

            # 同时设置环境变量
            os.environ['FFMPEG_BINARY'] = self.ffmpeg_path
            if os.path.exists(ffprobe_path):
                os.environ['FFPROBE_BINARY'] = ffprobe_path

            # 设置pydub的subprocess参数，隐藏窗口
            if platform.system() == "Windows":
                os.environ['PYDUB_SUBPROCESS_CREATION_FLAGS'] = str(subprocess.CREATE_NO_WINDOW)

            print(f"pydub FFmpeg路径更新: {self.ffmpeg_path}")
            if os.path.exists(ffprobe_path):
                print(f"pydub FFprobe路径更新: {ffprobe_path}")

            # 重新应用补丁，确保新的FFmpeg路径也使用隐藏窗口
            _patch_pydub_subprocess()

            return True
        return False

    def is_local_stt_available(self):
        """检查本地语音识别是否可用"""
        return self.use_local_stt

    async def load_voices_async(self):
        """
        异步加载可用的语音列表

        返回:
            dict: 语音字典 {voice_id: display_name}
        """
        try:
            # 获取所有可用的语音
            all_voices = await edge_tts.list_voices()

            # 筛选中文语音
            chinese_voices = {}
            for voice in all_voices:
                if voice['Locale'].startswith('zh-CN'):
                    voice_id = voice['ShortName']
                    # 提取性别信息
                    gender = "女声" if voice['Gender'] == 'Female' else "男声"
                    # 构建显示名称
                    display_name = f"{voice['FriendlyName']} ({gender})"
                    chinese_voices[voice_id] = display_name

            return chinese_voices

        except Exception as e:
            print(f"获取语音列表失败: {e}")
            # 返回默认的语音列表
            return {
                "zh-CN-XiaoxiaoNeural": "Microsoft Xiaoxiao (女声)",
                "zh-CN-YunxiNeural": "Microsoft Yunxi (男声)",
                "zh-CN-XiaoyiNeural": "Microsoft Xiaoyi (女声)",
                "zh-CN-YunjianNeural": "Microsoft Yunjian (男声)"
            }

    def load_voices(self):
        """
        同步加载语音列表

        返回:
            dict: 语音字典
        """
        if not self.voices_loaded:
            try:
                self.voices = asyncio.run(self.load_voices_async())
                self.voices_loaded = True
            except Exception as e:
                print(f"加载语音列表失败: {e}")
                # 使用默认语音列表
                self.voices = {
                    "zh-CN-XiaoxiaoNeural": "Microsoft Xiaoxiao (女声)",
                    "zh-CN-YunxiNeural": "Microsoft Yunxi (男声)",
                    "zh-CN-XiaoyiNeural": "Microsoft Xiaoyi (女声)",
                    "zh-CN-YunjianNeural": "Microsoft Yunjian (男声)"
                }
                self.voices_loaded = True

        return self.voices

    def get_voice_list(self):
        """
        获取语音列表（如果未加载则先加载）

        返回:
            list: 语音ID列表
        """
        if not self.voices_loaded:
            self.load_voices()
        return list(self.voices.keys())

    def get_voice_display_name(self, voice_id):
        """
        获取语音的显示名称

        参数:
            voice_id (str): 语音ID

        返回:
            str: 显示名称
        """
        if not self.voices_loaded:
            self.load_voices()
        return self.voices.get(voice_id, voice_id)

    def pause_processing(self):
        """暂停处理"""
        self.is_paused = True

    def resume_processing(self):
        """继续处理"""
        self.is_paused = False
        
    def split_text_for_tts(self, text):
        """
        将长文本分割成适合TTS的片段
        优化版本：避免在句子中间分割，减少不自然的停顿

        参数:
            text (str): 输入文本

        返回:
            list: 文本片段列表
        """
        if len(text) <= self.max_segment_length:
            return [text]

        segments = []
        current_pos = 0

        while current_pos < len(text):
            # 计算当前段的结束位置
            end_pos = min(current_pos + self.max_segment_length, len(text))

            # 如果不是最后一段，智能寻找分割点
            if end_pos < len(text):
                best_split = self._find_best_split_point(text, current_pos, end_pos)
                if best_split != -1:
                    end_pos = best_split

            # 提取当前段
            segment = text[current_pos:end_pos].strip()
            if segment:
                segments.append(segment)

            current_pos = end_pos

        return segments

    def _find_best_split_point(self, text, start_pos, ideal_end_pos):
        """
        智能寻找最佳分割点，避免在句子中间分割

        参数:
            text (str): 完整文本
            start_pos (int): 当前段开始位置
            ideal_end_pos (int): 理想结束位置

        返回:
            int: 最佳分割点位置，-1表示未找到
        """
        # 优先级1: 段落分隔符（双换行）
        paragraph_split = text.rfind('\n\n', start_pos, ideal_end_pos)
        if paragraph_split != -1 and paragraph_split > start_pos + self.max_segment_length // 4:
            return paragraph_split + 2

        # 优先级2: 句子结束标点（包括引号结束）
        sentence_ends = ['。', '！', '？', '.', '!', '?']
        quote_ends = ['"', '"', "'", '」', '』', '》']

        best_sentence_split = -1

        # 从理想位置向前搜索句子结束点
        search_start = max(start_pos + self.max_segment_length // 3, start_pos + 200)  # 增加最小长度
        for i in range(ideal_end_pos - 1, search_start - 1, -1):
            if text[i] in sentence_ends:
                # 检查是否是真正的句子结束
                if self._is_real_sentence_end(text, i):
                    # 检查后面是否有引号，如果有则包含引号
                    next_pos = i + 1
                    while next_pos < len(text) and text[next_pos] in quote_ends:
                        next_pos += 1
                    best_sentence_split = next_pos
                    break

        if best_sentence_split != -1:
            return best_sentence_split

        # 优先级3: 对话结束（引号后的标点）
        for i in range(ideal_end_pos - 1, search_start - 1, -1):
            if text[i] in quote_ends and i + 1 < len(text):
                # 检查引号后是否有合适的分割点
                next_char = text[i + 1]
                if next_char in ['，', '。', '！', '？', '.', '!', '?', '\n', ' ']:
                    return i + 1

        # 优先级4: 避免在关键词中间分割
        # 检查是否会在常见词汇中间分割
        if self._would_split_important_phrase(text, ideal_end_pos):
            # 向前寻找更安全的分割点
            for i in range(ideal_end_pos - 50, search_start - 1, -1):
                if text[i] in ['，', '；', ',', ';', '\n', ' ', '　']:
                    if not self._would_split_important_phrase(text, i):
                        return i + 1

        # 优先级5: 逗号或分号（但要确保不会产生太短的片段）
        comma_split = -1
        for i in range(ideal_end_pos - 1, search_start - 1, -1):
            if text[i] in ['，', '；', ',', ';']:
                # 确保分割后的片段不会太短，且不会分割重要短语
                if (i - start_pos >= self.max_segment_length // 2 and
                    not self._would_split_important_phrase(text, i + 1)):
                    comma_split = i + 1
                    break

        if comma_split != -1:
            return comma_split

        # 优先级6: 单个换行符
        newline_split = text.rfind('\n', search_start, ideal_end_pos)
        if newline_split != -1:
            return newline_split + 1

        # 优先级7: 空格（中英文）- 但要避免分割重要短语
        space_split = -1
        for i in range(ideal_end_pos - 1, search_start - 1, -1):
            if text[i] in [' ', '\t', '　']:  # 包括中文全角空格
                if not self._would_split_important_phrase(text, i + 1):
                    space_split = i + 1
                    break

        if space_split != -1:
            return space_split

        # 如果都找不到合适的分割点，返回-1（使用原始位置）
        return -1

    def _would_split_important_phrase(self, text, split_pos):
        """
        检查在指定位置分割是否会破坏重要短语

        参数:
            text (str): 文本
            split_pos (int): 分割位置

        返回:
            bool: 是否会分割重要短语
        """
        # 定义不应该被分割的重要短语模式
        important_phrases = [
            # 常见人物称谓
            r'什么样的\w+',
            r'\w+爱豆',
            r'\w+粉丝',
            # 常见动作描述
            r'小声\w+',
            r'大声\w+',
            r'朗声\w+',
            # 常见形容词组合
            r'非常\w+',
            r'特别\w+',
            r'十分\w+',
            # 其他常见短语
            r'然后\w+',
            r'接着\w+',
            r'于是\w+',
        ]

        # 检查分割点前后的文本
        check_range = 10  # 检查前后10个字符
        start_check = max(0, split_pos - check_range)
        end_check = min(len(text), split_pos + check_range)
        context = text[start_check:end_check]

        # 检查是否匹配重要短语模式
        import re
        for pattern in important_phrases:
            if re.search(pattern, context):
                # 进一步检查分割点是否在短语中间
                matches = list(re.finditer(pattern, context))
                for match in matches:
                    phrase_start = start_check + match.start()
                    phrase_end = start_check + match.end()
                    if phrase_start < split_pos < phrase_end:
                        return True

        return False

    def _is_real_sentence_end(self, text, pos):
        """
        判断是否是真正的句子结束

        参数:
            text (str): 文本
            pos (int): 标点位置

        返回:
            bool: 是否是真正的句子结束
        """
        # 检查是否是省略号的一部分
        if pos > 0 and text[pos-1:pos+1] in ['..', '。。']:
            return False

        # 检查是否在引号内
        if pos < len(text) - 1:
            next_char = text[pos + 1]
            if next_char in ['"', '"', "'", '」', '』']:
                return True

        # 检查后面是否紧跟数字（可能是小数点）
        if text[pos] == '.' and pos < len(text) - 1:
            if text[pos + 1].isdigit():
                return False

        return True

    def enhance_audio_quality(self, audio_path, enhance_clarity=True, normalize_volume=True):
        """
        增强音频质量，提升咬字清晰度

        参数:
            audio_path (str): 音频文件路径
            enhance_clarity (bool): 是否增强清晰度
            normalize_volume (bool): 是否标准化音量

        返回:
            bool: 是否成功处理
        """
        try:
            # 优先使用FFmpeg快速处理清晰度增强
            if enhance_clarity:
                success = self._enhance_clarity_fast(audio_path)
                if success:
                    return True
                else:
                    # FFmpeg失败时回退到pydub方法
                    print("FFmpeg处理失败，回退到pydub方法...")
                    audio = AudioSegment.from_file(audio_path)
                    audio = self._enhance_clarity(audio)
                    audio.export(audio_path, format="mp3", bitrate="192k")
                    return True

            # 如果只需要音量标准化（目前已禁用）
            if normalize_volume and not enhance_clarity:
                audio = AudioSegment.from_file(audio_path)
                audio = self._normalize_volume(audio)
                audio.export(audio_path, format="mp3", bitrate="192k")
                return True

            return True

        except Exception as e:
            print(f"音频质量增强失败: {str(e)}")
            return False

    def _check_ffmpeg_available(self, ffmpeg_exe):
        """检查FFmpeg是否可用"""
        try:
            import platform
            if platform.system() == "Windows":
                result = subprocess.run([ffmpeg_exe, '-version'],
                                      capture_output=True, text=True, timeout=5,
                                      creationflags=subprocess.CREATE_NO_WINDOW)
            else:
                result = subprocess.run([ffmpeg_exe, '-version'],
                                      capture_output=True, text=True, timeout=5)
            return result.returncode == 0
        except:
            return False

    def _enhance_clarity_fast(self, audio_path):
        """
        使用FFmpeg快速增强音频清晰度（比pydub快很多）

        参数:
            audio_path (str): 音频文件路径

        返回:
            bool: 是否成功处理
        """
        try:
            import subprocess
            import tempfile
            import os

            # 创建临时文件
            temp_output = tempfile.mktemp(suffix='.mp3')

            try:
                # 获取FFmpeg路径
                ffmpeg_path = config_manager.get_ffmpeg_path()
                ffmpeg_exe = None

                # 尝试多种FFmpeg路径
                if ffmpeg_path:
                    # 检查配置的路径
                    possible_paths = [
                        os.path.join(ffmpeg_path, 'ffmpeg.exe'),  # 直接在配置路径下
                        os.path.join(ffmpeg_path, 'bin', 'ffmpeg.exe'),  # 在bin子目录下
                        ffmpeg_path if ffmpeg_path.endswith('ffmpeg.exe') else None  # 配置的就是exe文件
                    ]

                    for path in possible_paths:
                        if path and os.path.exists(path):
                            ffmpeg_exe = path
                            break

                # 如果配置路径都不行，尝试系统PATH
                if not ffmpeg_exe:
                    ffmpeg_exe = 'ffmpeg'

                # 检查FFmpeg是否可用
                if not self._check_ffmpeg_available(ffmpeg_exe):
                    print(f"FFmpeg不可用: {ffmpeg_exe}")
                    return False

                # 使用FFmpeg进行快速音频处理
                # highpass=80: 高通滤波去除低频噪音
                # volume=1.2: 轻微增益提升音量
                # 这比pydub快很多，因为是原生C代码处理
                cmd = [
                    ffmpeg_exe, '-i', audio_path,
                    '-af', 'highpass=f=80,volume=1.2',
                    '-acodec', 'mp3', '-ab', '192k',
                    '-y', temp_output
                ]

                # 添加调试信息
                print(f"使用FFmpeg路径: {ffmpeg_exe}")
                print(f"FFmpeg命令: {' '.join(cmd)}")

                # 执行FFmpeg命令（隐藏窗口）
                print(f"系统类型: {platform.system()}")
                if platform.system() == "Windows":
                    print("使用Windows模式，设置CREATE_NO_WINDOW")
                    result = subprocess.run(cmd, capture_output=True, text=True, encoding='utf-8', errors='ignore',
                                          creationflags=subprocess.CREATE_NO_WINDOW)
                else:
                    print("使用非Windows模式")
                    result = subprocess.run(cmd, capture_output=True, text=True, encoding='utf-8', errors='ignore')

                if result.returncode == 0:
                    # 替换原文件
                    import shutil
                    shutil.move(temp_output, audio_path)
                    print("FFmpeg清晰度增强成功")
                    return True
                else:
                    print(f"FFmpeg清晰度增强失败: {result.stderr}")
                    return False

            finally:
                # 确保临时文件被清理
                if os.path.exists(temp_output):
                    try:
                        os.unlink(temp_output)
                    except Exception:
                        pass

        except FileNotFoundError as e:
            print(f"FFmpeg可执行文件未找到: {ffmpeg_exe}")
            print("请检查FFmpeg路径设置或确保FFmpeg已安装")
            return False
        except Exception as e:
            print(f"快速清晰度增强失败: {str(e)}")
            return False

    def _enhance_clarity(self, audio):
        """
        增强音频清晰度（保留原方法作为备用）

        参数:
            audio (AudioSegment): 音频对象

        返回:
            AudioSegment: 增强后的音频
        """
        try:
            # 1. 高通滤波 - 去除低频噪音，突出人声
            audio = audio.high_pass_filter(80)

            # 2. 轻微增益 - 提升整体音量
            audio = audio + 2  # 增加2dB

            return audio

        except Exception as e:
            print(f"清晰度增强失败: {str(e)}")
            return audio

    def _normalize_volume(self, audio):
        """
        标准化音频音量

        参数:
            audio (AudioSegment): 音频对象

        返回:
            AudioSegment: 标准化后的音频
        """
        try:
            # 计算目标音量 (-20dB，适合语音)
            target_dBFS = -20.0

            # 计算当前音量
            current_dBFS = audio.dBFS

            # 计算需要调整的音量
            change_in_dBFS = target_dBFS - current_dBFS

            # 应用音量调整
            normalized_audio = audio + change_in_dBFS

            return normalized_audio

        except Exception as e:
            print(f"音量标准化失败: {str(e)}")
            return audio

    def boost_volume(self, audio_path, volume_value):
        """
        提升音频音量（用于超过100%的音量设置）
        使用先进的音频处理技术避免破音和失真

        参数:
            audio_path (str): 音频文件路径
            volume_value (int): 音量值 (0 到 300)

        返回:
            bool: 是否成功处理
        """
        try:
            # 只有当音量超过100时才进行额外提升
            if volume_value <= 100:
                return True

            # 计算目标响度增益
            extra_boost = volume_value - 100  # 0-200的额外提升

            # 转换为更保守的dB增益（避免过度放大）
            # 使用对数缩放，高音量时增益更小
            if extra_boost <= 50:  # 100%-150%
                db_boost = extra_boost * 0.12  # 最多6dB
            elif extra_boost <= 100:  # 150%-200%
                db_boost = 6 + (extra_boost - 50) * 0.08  # 6-10dB
            else:  # 200%-300%
                db_boost = 10 + (extra_boost - 100) * 0.05  # 10-15dB

            # 限制最大增益为15dB
            db_boost = min(db_boost, 15.0)

            # 优先使用FFmpeg进行高质量音频处理
            if self.is_ffmpeg_available():
                return self._boost_volume_with_ffmpeg(audio_path, db_boost)
            else:
                return self._boost_volume_with_pydub(audio_path, db_boost)

        except Exception as e:
            print(f"音量提升失败: {str(e)}")
            return False

    def _boost_volume_with_ffmpeg(self, audio_path, db_boost):
        """
        使用FFmpeg进行高质量音量提升，避免削波失真

        参数:
            audio_path (str): 音频文件路径
            db_boost (float): dB增益值

        返回:
            bool: 是否成功处理
        """
        try:
            import tempfile

            # 创建临时文件
            temp_file = tempfile.NamedTemporaryFile(suffix='.mp3', delete=False)
            temp_path = temp_file.name
            temp_file.close()

            # 构建FFmpeg命令 - 使用多级音频处理避免失真
            cmd = [
                self.ffmpeg_path, '-y',
                '-i', audio_path,
                '-af', (
                    # 1. 动态音频标准化 - 避免削波
                    f'dynaudnorm=p=0.9:m=10:s=5,'
                    # 2. 音量增益
                    f'volume={db_boost}dB,'
                    # 3. 软限制器 - 防止超过0dBFS
                    'alimiter=level_in=1:level_out=0.95:limit=0.95:attack=5:release=50,'
                    # 4. 轻微压缩 - 控制动态范围
                    'acompressor=threshold=0.1:ratio=2:attack=10:release=100'
                ),
                '-c:a', 'libmp3lame',
                '-b:a', '192k',
                '-ar', '44100',
                temp_path
            ]

            # 执行FFmpeg命令
            if platform.system() == "Windows":
                result = subprocess.run(cmd, capture_output=True, text=True,
                                      creationflags=subprocess.CREATE_NO_WINDOW)
            else:
                result = subprocess.run(cmd, capture_output=True, text=True)

            if result.returncode == 0:
                # 替换原文件
                shutil.move(temp_path, audio_path)
                print(f"FFmpeg音量提升成功: +{db_boost:.1f}dB")
                return True
            else:
                print(f"FFmpeg音量提升失败: {result.stderr}")
                # 清理临时文件
                try:
                    os.unlink(temp_path)
                except:
                    pass
                # 回退到pydub方法
                return self._boost_volume_with_pydub(audio_path, db_boost)

        except Exception as e:
            print(f"FFmpeg音量提升异常: {str(e)}")
            # 回退到pydub方法
            return self._boost_volume_with_pydub(audio_path, db_boost)

    def _boost_volume_with_pydub(self, audio_path, db_boost):
        """
        使用pydub进行音量提升（备选方案）

        参数:
            audio_path (str): 音频文件路径
            db_boost (float): dB增益值

        返回:
            bool: 是否成功处理
        """
        try:
            # 加载音频
            audio = AudioSegment.from_file(audio_path)

            # 应用音量提升（更保守的增益）
            boosted_audio = audio + db_boost

            # 检查是否有削波风险
            if boosted_audio.max_possible_amplitude > 0:
                # 如果有削波风险，进行软限制
                max_amplitude = boosted_audio.max_possible_amplitude
                current_max = max(abs(sample) for sample in boosted_audio.get_array_of_samples())

                if current_max > max_amplitude * 0.95:  # 如果接近削波
                    # 应用软限制，降低增益
                    reduction_factor = (max_amplitude * 0.95) / current_max
                    boosted_audio = boosted_audio.apply_gain(20 * math.log10(reduction_factor))
                    print(f"应用软限制，避免削波: 降低 {-20 * math.log10(reduction_factor):.1f}dB")

            # 保存增强后的音频
            boosted_audio.export(audio_path, format="mp3", bitrate="192k")
            print(f"pydub音量提升成功: +{db_boost:.1f}dB")
            return True

        except Exception as e:
            print(f"pydub音量提升失败: {str(e)}")
            return False

    def apply_final_audio_processing(self, audio_path, volume_raw=None, progress_callback=None):
        """
        应用最终的音频后处理（音质增强、音量提升）

        参数:
            audio_path (str): 音频文件路径
            volume_raw (int): 原始音量值（0-300），用于音量提升
            progress_callback (function): 进度回调函数

        返回:
            bool: 是否成功处理
        """
        try:
            if not os.path.exists(audio_path):
                return False

            # 1. 增强音频清晰度
            if self.enhance_clarity:
                if progress_callback:
                    progress_callback("正在增强咬字清晰度...")

                quality_enhanced = self.enhance_audio_quality(
                    audio_path,
                    enhance_clarity=True,
                    normalize_volume=False  # 简化处理，不进行音量标准化
                )

                if quality_enhanced and progress_callback:
                    progress_callback("咬字清晰度增强完成")

            # 2. 额外音量提升（超过100%的部分）
            if volume_raw and volume_raw > 100:
                if progress_callback:
                    progress_callback(f"正在智能提升音量至{volume_raw}%...")

                volume_boosted = self.boost_volume(audio_path, volume_raw)

                if volume_boosted:
                    if progress_callback:
                        progress_callback("音量提升完成，已应用防破音处理")
                else:
                    if progress_callback:
                        progress_callback("音量提升失败，使用原始音量")

            return True

        except Exception as e:
            if progress_callback:
                progress_callback(f"音频后处理失败: {str(e)}")
            return False

    # 移除停顿功能已禁用
    # def remove_excessive_pauses(self, audio_path, max_pause_ms=100, silence_threshold=-40):
    #     """
    #     检测并移除音频中的过长停顿（已禁用）
    #     """
    #     pass

    # 静音检测功能已禁用
    # def _detect_silence_ranges(self, audio, silence_threshold=-40, min_silence_len=50):
    #     """
    #     检测音频中的静音片段（已禁用）
    #     """
    #     pass
    
    def text_to_speech_cli(self, text, voice, rate, pitch, volume, output_path, subtitle_path=None, progress_callback=None, volume_raw=None):
        """
        使用edge-tts生成语音（优先使用Python API，兼容打包环境）

        参数:
            text (str): 要转换的文本
            voice (str): 语音名称
            rate (str): 语速
            pitch (str): 音调
            volume (str): 音量（SSML格式）
            output_path (str): 输出文件路径
            subtitle_path (str): 字幕文件路径（可选）
            progress_callback (function): 进度回调函数
            volume_raw (int): 原始音量值（0-300），用于后处理
        """
        try:
            if progress_callback:
                progress_callback("正在生成语音...")

            # 确保参数都是字符串类型
            rate_str = str(rate) if rate is not None else "+0%"
            pitch_str = str(pitch) if pitch is not None else "+0Hz"
            volume_str = str(volume) if volume is not None else "+0%"

            # 创建Communicate对象（只传入非默认参数）
            communicate_kwargs = {
                'text': text,
                'voice': voice
            }

            # 只有非默认值才添加参数
            if rate_str != "+0%":
                communicate_kwargs['rate'] = rate_str
            if pitch_str != "+0Hz":
                communicate_kwargs['pitch'] = pitch_str
            if volume_str != "+0%":
                communicate_kwargs['volume'] = volume_str

            # 记录调试信息
            logger.info(f"TTS参数: voice={voice}, rate={rate_str}, pitch={pitch_str}, volume={volume_str}")

            communicate = edge_tts.Communicate(**communicate_kwargs)

            # 如果需要字幕，使用流式处理
            if subtitle_path:
                success = self.save_with_subtitles_sync(communicate, output_path, subtitle_path, progress_callback)

                # 注意：音频后处理将在最终合并后统一进行，这里不做处理
                return success
            else:
                # 直接保存音频（同步方式）
                if hasattr(communicate, 'save_sync'):
                    # 新版本edge-tts有同步方法
                    communicate.save_sync(output_path)
                else:
                    # 旧版本使用异步方法的同步包装
                    async def save_audio():
                        await communicate.save(output_path)

                    self._run_async_safely(save_audio)

                if progress_callback:
                    progress_callback("语音生成成功")

                # 注意：音频后处理将在最终合并后统一进行，这里不做处理
                return True

        except Exception as e:
            # 记录详细错误信息
            logger.log_tts_error(
                text=text,
                voice=voice,
                rate=rate,
                pitch=pitch,
                volume=volume,
                error_message=f"edge-tts处理失败: {str(e)}",
                exception=e
            )

            if progress_callback:
                progress_callback(f"语音生成失败: {str(e)}")

            return False

    def _run_async_safely(self, async_func, *args, **kwargs):
        """安全地在多线程环境中运行异步函数"""
        import asyncio
        import threading
        import time

        try:
            # 尝试获取当前线程的事件循环
            try:
                loop = asyncio.get_event_loop()
                if loop.is_running():
                    # 如果循环正在运行，创建新的循环
                    loop = None
            except RuntimeError:
                loop = None

            if loop is None:
                # 创建新的事件循环，使用SelectorEventLoop避免Windows ProactorEventLoop问题
                if hasattr(asyncio, 'WindowsSelectorEventLoopPolicy'):
                    # Windows环境下使用SelectorEventLoop
                    policy = asyncio.WindowsSelectorEventLoopPolicy()
                    asyncio.set_event_loop_policy(policy)

                loop = asyncio.new_event_loop()
                asyncio.set_event_loop(loop)

                try:
                    # 运行异步函数
                    result = loop.run_until_complete(async_func(*args, **kwargs))

                    # 等待所有任务完成
                    pending = asyncio.all_tasks(loop)
                    if pending:
                        loop.run_until_complete(asyncio.gather(*pending, return_exceptions=True))

                    # 给一点时间让资源清理
                    time.sleep(0.1)

                    return result

                finally:
                    try:
                        # 安全关闭循环
                        if not loop.is_closed():
                            # 取消所有剩余任务
                            pending = asyncio.all_tasks(loop)
                            for task in pending:
                                task.cancel()

                            # 等待取消完成
                            if pending:
                                loop.run_until_complete(asyncio.gather(*pending, return_exceptions=True))

                            loop.close()
                    except Exception:
                        pass  # 忽略清理时的异常
            else:
                # 使用现有循环
                return loop.run_until_complete(async_func(*args, **kwargs))

        except Exception as e:
            logger.error(f"异步函数执行失败: {str(e)}", exception=e)
            raise

    def save_with_subtitles_sync(self, communicate, output_path, subtitle_path, progress_callback=None):
        """同步保存音频并生成字幕"""
        try:
            if progress_callback:
                progress_callback("正在生成音频和字幕...")

            # 使用SubMaker处理字幕
            submaker = edge_tts.SubMaker()

            # 同步流式处理
            with open(output_path, "wb") as audio_file:
                if hasattr(communicate, 'stream_sync'):
                    # 新版本edge-tts有同步流方法
                    for chunk in communicate.stream_sync():
                        if chunk["type"] == "audio":
                            audio_file.write(chunk["data"])
                        elif chunk["type"] == "WordBoundary":
                            submaker.feed(chunk)
                            # 移除频繁的进度更新，提高处理速度
                else:
                    # 旧版本使用异步方法的同步包装
                    async def process_stream():
                        async for chunk in communicate.stream():
                            if chunk["type"] == "audio":
                                audio_file.write(chunk["data"])
                            elif chunk["type"] == "WordBoundary":
                                submaker.feed(chunk)

                    self._run_async_safely(process_stream)

            # 生成字幕文件（分段处理时不进行优化，提高速度）
            if subtitle_path:
                raw_srt = submaker.get_srt()

                with open(subtitle_path, "w", encoding="utf-8") as subtitle_file:
                    subtitle_file.write(raw_srt)

                if progress_callback:
                    progress_callback("字幕文件生成成功")

            if progress_callback:
                progress_callback("语音和字幕生成成功")

            return True

        except Exception as e:
            logger.error(f"同步字幕处理失败: {str(e)}", exception=e)
            if progress_callback:
                progress_callback(f"字幕处理失败: {str(e)}")
            return False

    def _optimize_subtitle_format(self, raw_srt):
        """
        优化字幕格式，将5个字以下的短字幕合并到相邻字幕中

        参数:
            raw_srt (str): 原始SRT字幕内容

        返回:
            str: 优化后的SRT字幕内容
        """
        try:
            if not raw_srt.strip():
                return raw_srt

            # 解析SRT条目
            entries = []
            blocks = raw_srt.strip().split('\n\n')

            for block in blocks:
                lines = block.strip().split('\n')
                if len(lines) >= 3:
                    try:
                        index = int(lines[0])
                        time_line = lines[1]
                        text = '\n'.join(lines[2:])

                        # 解析时间
                        start_time, end_time = time_line.split(' --> ')
                        start_seconds = self._srt_time_to_seconds(start_time)
                        end_seconds = self._srt_time_to_seconds(end_time)

                        entries.append({
                            'index': index,
                            'start': start_seconds,
                            'end': end_seconds,
                            'text': text.strip()
                        })
                    except (ValueError, IndexError):
                        continue

            if not entries:
                return raw_srt

            # 简化的合并策略：只将5个字以下的短字幕合并到相邻字幕
            optimized_entries = []
            i = 0

            while i < len(entries):
                current_entry = entries[i].copy()

                # 检查当前字幕是否为短字幕（5个字以下）
                if len(current_entry['text']) <= 5:
                    # 尝试与前一个字幕合并
                    if optimized_entries and len(optimized_entries[-1]['text']) < 20:
                        # 合并到前一个字幕
                        prev_entry = optimized_entries[-1]
                        prev_entry['text'] += current_entry['text']
                        prev_entry['end'] = current_entry['end']
                    # 尝试与后一个字幕合并
                    elif i + 1 < len(entries) and len(entries[i + 1]['text']) < 20:
                        # 合并到后一个字幕
                        next_entry = entries[i + 1].copy()
                        next_entry['text'] = current_entry['text'] + next_entry['text']
                        next_entry['start'] = current_entry['start']
                        optimized_entries.append(next_entry)
                        i += 1  # 跳过下一个条目，因为已经合并了
                    else:
                        # 无法合并，保持原样
                        optimized_entries.append(current_entry)
                else:
                    # 长字幕保持原样
                    optimized_entries.append(current_entry)

                i += 1

            # 重新生成SRT格式
            optimized_srt = ""
            for i, entry in enumerate(optimized_entries, 1):
                start_time = self._seconds_to_srt_time(entry['start'])
                end_time = self._seconds_to_srt_time(entry['end'])

                optimized_srt += f"{i}\n"
                optimized_srt += f"{start_time} --> {end_time}\n"
                optimized_srt += f"{entry['text']}\n\n"

            return optimized_srt.strip()

        except Exception as e:
            logger.error(f"字幕优化失败: {str(e)}")
            # 如果优化失败，返回原始字幕
            return raw_srt



    def text_to_speech_command_line(self, text, voice, rate, pitch, volume, output_path, subtitle_path=None, progress_callback=None, volume_raw=None):
        """
        使用edge-tts命令行方式生成语音（备用方案）
        """
        try:
            # 构建edge-tts命令
            cmd = ["edge-tts"]

            # 添加文本参数
            cmd.extend(["--text", text])

            # 添加语音参数
            cmd.extend(["--voice", voice])

            # 添加输出文件参数
            cmd.extend(["--write-media", output_path])

            # 添加字幕输出参数
            if subtitle_path:
                cmd.extend(["--write-subtitles", subtitle_path])

            # 添加语音参数（只有非默认值才添加）
            if rate != "+0%":
                if rate.startswith("-"):
                    cmd.append(f"--rate={rate}")
                else:
                    cmd.extend(["--rate", rate])
            if pitch != "+0Hz":
                if pitch.startswith("-"):
                    cmd.append(f"--pitch={pitch}")
                else:
                    cmd.extend(["--pitch", pitch])
            if volume != "+0%":
                if volume.startswith("-"):
                    cmd.append(f"--volume={volume}")
                else:
                    cmd.extend(["--volume", volume])

            if progress_callback:
                progress_callback(f"执行命令: {' '.join(cmd[:6])}...")

            # 执行命令（隐藏窗口）
            import platform
            if platform.system() == "Windows":
                result = subprocess.run(
                    cmd,
                    capture_output=True,
                    text=True,
                    timeout=60,
                    encoding='utf-8',
                    creationflags=subprocess.CREATE_NO_WINDOW
                )
            else:
                result = subprocess.run(
                    cmd,
                    capture_output=True,
                    text=True,
                    timeout=60,
                    encoding='utf-8'
                )

            if result.returncode == 0:
                if progress_callback:
                    progress_callback("语音生成成功")
                return True
            else:
                error_msg = result.stderr or result.stdout or "未知错误"
                logger.error(
                    f"edge-tts命令执行失败\n"
                    f"命令: {' '.join(cmd)}\n"
                    f"返回码: {result.returncode}\n"
                    f"标准输出: {result.stdout}\n"
                    f"错误输出: {result.stderr}"
                )
                return False

        except subprocess.TimeoutExpired as e:
            logger.error(f"edge-tts命令执行超时", exception=e)
            return False
        except Exception as e:
            logger.error(f"edge-tts命令执行异常", exception=e)
            return False

    def text_to_speech_with_retry(self, text, voice, rate, pitch, volume, output_path, subtitle_path=None, progress_callback=None, segment_index=None, volume_raw=None):
        """
        带重试机制的文本转语音

        参数:
            text (str): 要转换的文本
            voice (str): 语音名称
            rate (str): 语速
            pitch (str): 音调
            volume (str): 音量（SSML格式）
            output_path (str): 输出文件路径
            subtitle_path (str): 字幕文件路径（可选）
            progress_callback (function): 进度回调函数
            segment_index (int): 分段索引（用于错误报告）
            volume_raw (int): 原始音量值（0-300），用于后处理

        返回:
            bool: 是否成功
        """
        for attempt in range(self.retry_count + 1):
            try:
                if self.should_stop:
                    return False

                if progress_callback and attempt > 0:
                    progress_callback(f"分段 {segment_index + 1 if segment_index is not None else '?'} 重试第 {attempt} 次...")

                success = self.text_to_speech_cli(
                    text, voice, rate, pitch, volume,
                    output_path, subtitle_path, progress_callback, volume_raw
                )

                if success:
                    return True

                # 如果失败且还有重试次数，等待一段时间后重试
                if attempt < self.retry_count:
                    if progress_callback:
                        progress_callback(f"分段 {segment_index + 1 if segment_index is not None else '?'} 失败，{self.retry_interval}秒后重试...")
                    time.sleep(self.retry_interval)

            except Exception as e:
                error_msg = f"分段 {segment_index + 1 if segment_index is not None else '?'} 处理异常: {str(e)}"
                if progress_callback:
                    progress_callback(error_msg)

                # 记录详细错误日志
                logger.log_tts_error(
                    text=text,
                    voice=voice,
                    rate=rate,
                    pitch=pitch,
                    volume=volume,
                    error_message=f"第 {attempt + 1} 次尝试失败",
                    exception=e
                )

                if attempt < self.retry_count:
                    time.sleep(self.retry_interval)
                else:
                    # 最后一次尝试也失败了，记录最终失败日志
                    logger.log_segment_error(
                        segment_index=segment_index if segment_index is not None else -1,
                        segment_text=text,
                        error_message=f"所有重试都失败，最后一次异常: {str(e)}",
                        exception=e
                    )
                    return False

        # 所有重试都失败了
        final_error_msg = f"分段 {segment_index + 1 if segment_index is not None else '?'} 处理失败，已达到最大重试次数"
        if progress_callback:
            progress_callback(final_error_msg)

        # 记录最终失败日志
        logger.log_segment_error(
            segment_index=segment_index if segment_index is not None else -1,
            segment_text=text,
            error_message="达到最大重试次数，所有尝试都失败"
        )
        return False
    
    def text_to_speech(self, text, voice, rate, pitch, volume, output_path, subtitle_path=None, progress_callback=None, volume_raw=None):
        """
        文本转语音（同步接口）

        参数:
            text (str): 要转换的文本
            voice (str): 语音名称
            rate (str): 语速
            pitch (str): 音调
            volume (str): 音量（SSML格式）
            output_path (str): 输出文件路径
            subtitle_path (str): 字幕文件路径（可选）
            progress_callback (function): 进度回调函数
            volume_raw (int): 原始音量值（0-300），用于后处理
        """
        try:
            # 分割文本
            segments = self.split_text_for_tts(text)

            if progress_callback:
                progress_callback(f"文本已分割为 {len(segments)} 个片段")

            if len(segments) == 1:
                # 只有一个片段，直接使用CLI生成
                return self.text_to_speech_cli(
                    segments[0], voice, rate, pitch, volume,
                    output_path, subtitle_path, progress_callback, volume_raw
                )
            else:
                # 多个片段，需要分别生成然后合并
                temp_files = []
                temp_subtitle_files = []

                for i, segment in enumerate(segments):
                    if progress_callback:
                        # 发送分段进度信息
                        segment_progress = {
                            "type": "segment_progress",
                            "current_segment": i + 1,
                            "total_segments": len(segments),
                            "segment_text": f"正在处理第 {i+1} 个分段",
                            "message": f"正在处理第 {i+1}/{len(segments)} 个片段..."
                        }
                        progress_callback(segment_progress)

                    # 创建临时文件
                    temp_audio = tempfile.NamedTemporaryFile(suffix='.mp3', delete=False)
                    temp_audio.close()
                    temp_files.append(temp_audio.name)

                    # 如果需要字幕，也创建临时字幕文件
                    temp_sub = None
                    if subtitle_path:
                        temp_sub = tempfile.NamedTemporaryFile(suffix='.srt', delete=False)
                        temp_sub.close()
                        temp_subtitle_files.append(temp_sub.name)

                    # 生成语音
                    success = self.text_to_speech_cli(
                        segment, voice, rate, pitch, volume,
                        temp_audio.name, temp_sub.name if temp_sub else None,
                        progress_callback, volume_raw
                    )

                    if not success:
                        # 清理临时文件
                        for f in temp_files + temp_subtitle_files:
                            try:
                                if os.path.exists(f):
                                    os.unlink(f)
                            except:
                                pass
                        return False

                # 合并音频文件
                if progress_callback:
                    progress_callback("正在合并音频片段...")

                if self.is_ffmpeg_available():
                    try:
                        # 使用FFmpeg直接合并音频（隐藏窗口）
                        self._merge_audio_with_ffmpeg_progress(temp_files, output_path, progress_callback)
                    except Exception as e:
                        if progress_callback:
                            progress_callback(f"FFmpeg合并失败，使用备用方案: {str(e)}")
                        self._simple_audio_merge_progress(temp_files, output_path, progress_callback)
                else:
                    self._simple_audio_merge_progress(temp_files, output_path, progress_callback)

                # 合并字幕文件
                if subtitle_path and temp_subtitle_files:
                    if progress_callback:
                        progress_callback("正在合并字幕文件...")
                    self._merge_subtitle_files(temp_subtitle_files, subtitle_path)

                    # 优化合并后的字幕格式
                    if progress_callback:
                        progress_callback("正在优化字幕格式...")
                    self._optimize_merged_subtitle_file(subtitle_path)

                # 清理临时文件
                for f in temp_files + temp_subtitle_files:
                    try:
                        if os.path.exists(f):
                            os.unlink(f)
                    except:
                        pass

                # 注意：音频后处理将在最终合并后统一进行，这里不做处理
                if progress_callback:
                    progress_callback("语音生成完成")

                return True

        except Exception as e:
            # 确保在异常情况下也清理临时文件
            try:
                for f in temp_files + temp_subtitle_files:
                    try:
                        if os.path.exists(f):
                            os.unlink(f)
                    except:
                        pass
            except:
                pass

            if progress_callback:
                progress_callback(f"语音生成失败: {str(e)}")
            raise e

    def _merge_audio_with_ffmpeg(self, temp_files, output_path, progress_callback=None):
        """
        使用FFmpeg合并音频文件（隐藏窗口）

        参数:
            temp_files (list): 临时音频文件列表
            output_path (str): 输出文件路径
            progress_callback (function): 进度回调函数
        """
        import tempfile
        import subprocess
        import platform

        if not temp_files:
            return

        # 获取FFmpeg路径
        ffmpeg_path = config_manager.get_ffmpeg_path()
        ffmpeg_exe = None

        if ffmpeg_path:
            possible_paths = [
                os.path.join(ffmpeg_path, 'ffmpeg.exe'),
                os.path.join(ffmpeg_path, 'bin', 'ffmpeg.exe'),
                ffmpeg_path if ffmpeg_path.endswith('ffmpeg.exe') else None
            ]

            for path in possible_paths:
                if path and os.path.exists(path):
                    ffmpeg_exe = path
                    break

        if not ffmpeg_exe:
            ffmpeg_exe = 'ffmpeg'

        # 创建concat文件
        concat_file = tempfile.NamedTemporaryFile(mode='w', suffix='.txt', delete=False, encoding='utf-8')
        try:
            for temp_file in temp_files:
                # 使用绝对路径，避免路径问题
                abs_path = os.path.abspath(temp_file).replace('\\', '/')
                concat_file.write(f"file '{abs_path}'\n")
            concat_file.close()

            # 构建FFmpeg命令，添加时间戳修复参数确保精确拼接
            cmd = [
                ffmpeg_exe, '-y',
                '-f', 'concat',
                '-safe', '0',
                '-i', concat_file.name,
                '-c', 'copy',  # 直接复制，不重新编码
                '-avoid_negative_ts', 'make_zero',  # 修复负时间戳
                '-fflags', '+genpts',  # 重新生成时间戳，确保连续性
                output_path
            ]

            if progress_callback:
                progress_callback("正在使用FFmpeg合并音频...")

            # 执行FFmpeg命令（隐藏窗口）
            print(f"FFmpeg合并 - 系统类型: {platform.system()}")
            if platform.system() == "Windows":
                print("FFmpeg合并 - 使用Windows模式，设置CREATE_NO_WINDOW")
                result = subprocess.run(cmd, capture_output=True, text=True, encoding='utf-8', errors='ignore',
                                      creationflags=subprocess.CREATE_NO_WINDOW)
            else:
                print("FFmpeg合并 - 使用非Windows模式")
                result = subprocess.run(cmd, capture_output=True, text=True, encoding='utf-8', errors='ignore')

            if result.returncode != 0:
                raise Exception(f"FFmpeg合并失败: {result.stderr}")

            if progress_callback:
                progress_callback("FFmpeg音频合并完成")

        finally:
            # 清理concat文件
            try:
                os.unlink(concat_file.name)
            except Exception:
                pass

    def _merge_audio_with_ffmpeg_progress(self, temp_files, output_path, progress_callback=None):
        """
        使用FFmpeg合并音频文件（带进度更新，防止超时）
        """
        import tempfile
        import subprocess
        import platform
        import threading
        import time

        if not temp_files:
            return

        # 获取FFmpeg路径
        ffmpeg_path = config_manager.get_ffmpeg_path()
        ffmpeg_exe = None

        if ffmpeg_path:
            possible_paths = [
                os.path.join(ffmpeg_path, 'ffmpeg.exe'),
                os.path.join(ffmpeg_path, 'bin', 'ffmpeg.exe'),
                ffmpeg_path if ffmpeg_path.endswith('ffmpeg.exe') else None
            ]

            for path in possible_paths:
                if path and os.path.exists(path):
                    ffmpeg_exe = path
                    break

        if not ffmpeg_exe:
            ffmpeg_exe = 'ffmpeg'

        # 创建concat文件
        concat_file = tempfile.NamedTemporaryFile(mode='w', suffix='.txt', delete=False, encoding='utf-8')
        try:
            for temp_file in temp_files:
                # 使用绝对路径，避免路径问题
                abs_path = os.path.abspath(temp_file).replace('\\', '/')
                concat_file.write(f"file '{abs_path}'\n")
            concat_file.close()

            # 构建FFmpeg命令，添加时间戳修复参数确保精确拼接
            cmd = [
                ffmpeg_exe, '-y',
                '-f', 'concat',
                '-safe', '0',
                '-i', concat_file.name,
                '-c', 'copy',  # 直接复制，不重新编码
                '-avoid_negative_ts', 'make_zero',  # 修复负时间戳
                '-fflags', '+genpts',  # 重新生成时间戳，确保连续性
                output_path
            ]

            if progress_callback:
                progress_callback("正在使用FFmpeg合并音频...")

            # 启动进程
            print(f"FFmpeg进度合并 - 系统类型: {platform.system()}")
            if platform.system() == "Windows":
                print("FFmpeg进度合并 - 使用Windows模式，设置CREATE_NO_WINDOW")
                process = subprocess.Popen(cmd, stdout=subprocess.PIPE, stderr=subprocess.PIPE,
                                         text=True, encoding='utf-8', errors='ignore',
                                         creationflags=subprocess.CREATE_NO_WINDOW)
            else:
                print("FFmpeg进度合并 - 使用非Windows模式")
                process = subprocess.Popen(cmd, stdout=subprocess.PIPE, stderr=subprocess.PIPE,
                                         text=True, encoding='utf-8', errors='ignore')

            # 注册进程到管理器
            ffmpeg_manager.register_process(process)

            # 进度监控线程
            def monitor_progress():
                last_update = time.time()
                heartbeat_count = 0
                while process.poll() is None:
                    current_time = time.time()
                    # 每10秒更新一次进度，防止超时
                    if current_time - last_update >= 10:
                        heartbeat_count += 1
                        if progress_callback:
                            progress_callback(f"正在合并音频文件... ({len(temp_files)} 个分段) [心跳 {heartbeat_count}]")
                        last_update = current_time
                    time.sleep(2)  # 更频繁的检查

            # 启动进度监控
            monitor_thread = threading.Thread(target=monitor_progress, daemon=True)
            monitor_thread.start()

            # 添加初始进度回调
            if progress_callback:
                progress_callback(f"开始FFmpeg合并 {len(temp_files)} 个音频分段...")

            # 等待进程完成，减少超时时间到8分钟
            try:
                stdout, stderr = process.communicate(timeout=480)  # 8分钟超时
            except subprocess.TimeoutExpired:
                # 超时后强制终止进程
                process.kill()
                process.wait()
                raise Exception(f"FFmpeg合并超时 (8分钟)，进程已被终止")
            finally:
                # 注销进程
                ffmpeg_manager.unregister_process(process)

            if process.returncode != 0:
                raise Exception(f"FFmpeg合并失败: {stderr}")

            if progress_callback:
                progress_callback("FFmpeg音频合并完成")

        finally:
            # 清理concat文件
            try:
                os.unlink(concat_file.name)
            except Exception:
                pass

    def _simple_audio_merge(self, temp_files, output_path):
        """
        简单的音频文件合并（不需要ffmpeg）
        直接将多个MP3文件的二进制内容连接
        """
        try:
            with open(output_path, 'wb') as output_file:
                for temp_file in temp_files:
                    with open(temp_file, 'rb') as input_file:
                        output_file.write(input_file.read())
        except Exception:
            # 如果简单合并也失败，至少保存第一个文件
            if temp_files:
                shutil.copy2(temp_files[0], output_path)

    def _simple_audio_merge_progress(self, temp_files, output_path, progress_callback=None):
        """
        简单的音频文件合并（带进度更新）
        """
        try:
            if progress_callback:
                progress_callback("正在合并音频文件...")

            with open(output_path, 'wb') as output_file:
                for i, temp_file in enumerate(temp_files):
                    if progress_callback and len(temp_files) > 1:
                        progress_callback(f"正在合并音频文件... ({i+1}/{len(temp_files)})")

                    with open(temp_file, 'rb') as input_file:
                        output_file.write(input_file.read())

            if progress_callback:
                progress_callback("音频合并完成")

        except Exception:
            # 如果简单合并也失败，至少保存第一个文件
            if temp_files:
                if progress_callback:
                    progress_callback("合并失败，保存第一个分段...")
                shutil.copy2(temp_files[0], output_path)

    def _merge_subtitle_files(self, subtitle_files, output_path):
        """
        合并多个SRT字幕文件

        参数:
            subtitle_files (list): 字幕文件路径列表
            output_path (str): 输出字幕文件路径
        """
        try:
            merged_content = []
            subtitle_index = 1
            time_offset = 0.0

            for subtitle_file in subtitle_files:
                if not os.path.exists(subtitle_file):
                    continue

                content = self._read_file_with_encoding_detection(subtitle_file).strip()

                if not content:
                    continue

                # 解析SRT内容
                entries = content.split('\n\n')

                for entry in entries:
                    lines = entry.strip().split('\n')
                    if len(lines) >= 3:
                        # 更新序号
                        lines[0] = str(subtitle_index)
                        subtitle_index += 1

                        # 更新时间轴（添加偏移）
                        if '-->' in lines[1]:
                            time_line = lines[1]
                            start_time, end_time = time_line.split(' --> ')

                            # 解析时间并添加偏移
                            start_seconds = self._srt_time_to_seconds(start_time) + time_offset
                            end_seconds = self._srt_time_to_seconds(end_time) + time_offset

                            # 转换回SRT格式
                            lines[1] = f"{self._seconds_to_srt_time(start_seconds)} --> {self._seconds_to_srt_time(end_seconds)}"

                            # 更新时间偏移（为下一个文件准备）
                            if subtitle_file == subtitle_files[-1] or subtitle_files.index(subtitle_file) == len(subtitle_files) - 1:
                                pass  # 最后一个文件，不需要更新偏移
                            else:
                                time_offset = end_seconds  # 无缝拼接，不添加间隔

                        merged_content.append('\n'.join(lines))

            # 保存合并后的字幕
            with open(output_path, 'w', encoding='utf-8') as f:
                f.write('\n\n'.join(merged_content))

        except Exception:
            # 如果合并失败，至少保存第一个字幕文件
            if subtitle_files and os.path.exists(subtitle_files[0]):
                shutil.copy2(subtitle_files[0], output_path)

    def _optimize_merged_subtitle_file(self, subtitle_path):
        """
        优化已合并的字幕文件

        参数:
            subtitle_path (str): 字幕文件路径
        """
        try:
            if not os.path.exists(subtitle_path):
                return

            # 读取原始字幕内容
            raw_content = self._read_file_with_encoding_detection(subtitle_path)

            # 优化字幕格式
            optimized_content = self._optimize_subtitle_format(raw_content)

            # 写回文件
            with open(subtitle_path, 'w', encoding='utf-8') as f:
                f.write(optimized_content)

        except Exception as e:
            logger.error(f"字幕文件优化失败: {str(e)}")

    def _srt_time_to_seconds(self, srt_time):
        """
        将SRT时间格式转换为秒数

        参数:
            srt_time (str): SRT时间格式 (HH:MM:SS,mmm)

        返回:
            float: 秒数
        """
        try:
            time_part, ms_part = srt_time.split(',')
            h, m, s = map(int, time_part.split(':'))
            ms = int(ms_part)
            return h * 3600 + m * 60 + s + ms / 1000.0
        except:
            return 0.0





    def _split_text_to_sentences(self, text):
        """
        将文本分割成句子

        参数:
            text (str): 输入文本

        返回:
            list: 句子列表
        """
        # 使用正则表达式分割句子
        sentence_endings = r'[。！？.!?]'
        sentences = re.split(sentence_endings, text)

        # 过滤空句子并清理
        sentences = [s.strip() for s in sentences if s.strip()]

        # 如果没有找到句子分隔符，按长度分割
        if len(sentences) <= 1 and text.strip():
            # 按照大约20个字符为一句进行分割
            words = text.strip()
            sentences = []
            for i in range(0, len(words), 20):
                sentences.append(words[i:i+20])

        return sentences

    def _seconds_to_srt_time(self, seconds):
        """
        将秒数转换为SRT时间格式

        参数:
            seconds (float): 秒数

        返回:
            str: SRT时间格式 (HH:MM:SS,mmm)
        """
        hours = int(seconds // 3600)
        minutes = int((seconds % 3600) // 60)
        secs = int(seconds % 60)
        milliseconds = int((seconds % 1) * 1000)

        return f"{hours:02d}:{minutes:02d}:{secs:02d},{milliseconds:03d}"
    
    def process_file(self, file_path, output_dir, voice, rate, pitch, volume,
                    generate_srt=True, progress_callback=None, volume_raw=None):
        """
        处理单个文件（支持多线程分段处理）

        参数:
            file_path (str): 输入文件路径
            output_dir (str): 输出目录
            voice (str): 语音名称
            rate (str): 语速
            pitch (str): 音调
            volume (str): 音量（SSML格式）
            generate_srt (bool): 是否生成字幕
            progress_callback (function): 进度回调函数
            volume_raw (int): 原始音量值（0-300），用于后处理
        """
        try:
            if self.should_stop:
                return {'success': False, 'error': '用户停止处理'}

            # 读取文件内容
            text = self._read_file_with_encoding_detection(file_path).strip()

            if not text:
                raise Exception("文件内容为空")

            # 获取文件名（不含扩展名）
            file_name = os.path.splitext(os.path.basename(file_path))[0]

            # 生成输出文件路径
            mp3_path = os.path.join(output_dir, f"{file_name}.mp3")
            srt_path = os.path.join(output_dir, f"{file_name}.srt")

            # 分割文本为段落
            segments = self.split_text_for_tts(text)

            if progress_callback:
                progress_callback({
                    "type": "segment_progress",
                    "current_segment": 0,
                    "total_segments": len(segments),
                    "segment_text": f"开始处理 {len(segments)} 个分段"
                })

            # 如果只有一个分段，直接处理
            if len(segments) == 1:
                success = self.text_to_speech_with_retry(
                    text, voice, rate, pitch, volume,
                    mp3_path, srt_path if generate_srt else None,
                    progress_callback, 0, volume_raw
                )

                if not success:
                    raise Exception("语音生成失败")

                # 应用最终的音频后处理
                if progress_callback:
                    progress_callback("正在进行音频后处理...")

                final_processing_success = self.apply_final_audio_processing(
                    mp3_path, volume_raw, progress_callback
                )

                if not final_processing_success:
                    if progress_callback:
                        progress_callback("音频后处理失败，但文件已生成")

                # 单文件处理完成后优化字幕格式
                if generate_srt and os.path.exists(srt_path):
                    if progress_callback:
                        progress_callback("正在优化字幕格式...")
                    self._optimize_merged_subtitle_file(srt_path)

                return {
                    'success': True,
                    'mp3_path': mp3_path,
                    'srt_path': srt_path if generate_srt else None
                }

            # 多分段处理 - 暂时使用线程池处理，确保稳定性
            # TODO: 异步处理需要进一步调试
            return self.process_segments_threaded(
                segments, file_name, output_dir, voice, rate, pitch, volume,
                generate_srt, progress_callback, volume_raw
            )

        except Exception as e:
            return {
                'success': False,
                'error': str(e)
            }

    def process_segments_async(self, segments, file_name, output_dir, voice, rate, pitch, volume, generate_srt, progress_callback, volume_raw=None):
        """
        异步并发处理文本分段 - 优化性能版本

        参数:
            segments (list): 文本分段列表
            file_name (str): 文件名（不含扩展名）
            output_dir (str): 输出目录
            voice (str): 语音名称
            rate (str): 语速
            pitch (str): 音调
            volume (str): 音量（SSML格式）
            generate_srt (bool): 是否生成字幕
            progress_callback (function): 进度回调函数
            volume_raw (int): 原始音量值（0-300），用于后处理

        返回:
            dict: 处理结果
        """
        try:
            # 使用安全的异步执行方法
            async def async_process():
                return await self._process_segments_async_impl(
                    segments, file_name, output_dir, voice, rate, pitch, volume,
                    generate_srt, progress_callback, volume_raw
                )

            result = self._run_async_safely(async_process)
            return result

        except Exception as e:
            error_msg = f"异步处理失败，回退到线程池处理: {str(e)}"
            if progress_callback:
                progress_callback(error_msg)

            # 记录异步处理失败的错误
            logger.error(f"异步分段处理失败: {error_msg}", exception=e)

            # 回退到原有的线程池处理
            return self.process_segments_threaded(
                segments, file_name, output_dir, voice, rate, pitch, volume,
                generate_srt, progress_callback, volume_raw
            )

    async def _process_segments_async_impl(self, segments, file_name, output_dir, voice, rate, pitch, volume, generate_srt, progress_callback, volume_raw=None):
        """异步处理实现"""
        segment_files = []
        failed_segments = []

        # 创建信号量来控制并发数量，避免过多请求
        semaphore = asyncio.Semaphore(min(self.thread_count, 10))  # 最多10个并发

        async def process_single_segment(index, segment_text):
            """处理单个分段（带重试机制）"""
            async with semaphore:
                # 重试机制
                for attempt in range(self.retry_count + 1):
                    try:
                        if self.should_stop:
                            return None

                        # 检查暂停状态
                        while self.is_paused and not self.should_stop:
                            await asyncio.sleep(1)

                        if self.should_stop:
                            return None

                        if attempt > 0 and progress_callback:
                            progress_callback(f"分段 {index + 1} 重试第 {attempt} 次...")

                        # 生成分段文件名
                        segment_mp3_path = os.path.join(output_dir, f"{file_name}_segment_{index:03d}.mp3")
                        segment_srt_path = os.path.join(output_dir, f"{file_name}_segment_{index:03d}.srt") if generate_srt else None

                        # 异步生成语音
                        success = await self._text_to_speech_async(
                            segment_text, voice, rate, pitch, volume,
                            segment_mp3_path, segment_srt_path, progress_callback, index, volume_raw
                        )

                        if success:
                            return {
                                'index': index,
                                'success': True,
                                'mp3_path': segment_mp3_path,
                                'srt_path': segment_srt_path,
                                'segment_text': segment_text[:100] + ('...' if len(segment_text) > 100 else '')
                            }
                        else:
                            # 如果失败且还有重试次数，等待后重试
                            if attempt < self.retry_count:
                                if progress_callback:
                                    progress_callback(f"分段 {index + 1} 失败，{self.retry_interval}秒后重试...")
                                await asyncio.sleep(self.retry_interval)
                                continue
                            else:
                                return {
                                    'index': index,
                                    'success': False,
                                    'error': f"分段 {index + 1} 语音生成失败（已重试{self.retry_count}次）",
                                    'segment_text': segment_text[:100] + ('...' if len(segment_text) > 100 else '')
                                }

                    except (asyncio.TimeoutError, OperationTimeoutError) as e:
                        error_msg = f"分段 {index + 1} 超时: {str(e)}"
                        if progress_callback:
                            progress_callback(error_msg)

                        # 超时也进行重试
                        if attempt < self.retry_count:
                            if progress_callback:
                                progress_callback(f"分段 {index + 1} 超时，{self.retry_interval}秒后重试...")
                            await asyncio.sleep(self.retry_interval)
                            continue
                        else:
                            return {
                                'index': index,
                                'success': False,
                                'error': error_msg,
                                'segment_text': segment_text[:100] + ('...' if len(segment_text) > 100 else '')
                            }

                    except Exception as e:
                        error_msg = f"分段 {index + 1} 处理异常: {str(e)}"
                        if progress_callback:
                            progress_callback(error_msg)

                        # 记录详细错误日志
                        logger.log_segment_error(
                            segment_index=index,
                            segment_text=segment_text,
                            error_message=f"异步处理异常 (尝试 {attempt + 1}/{self.retry_count + 1}): {str(e)}",
                            exception=e
                        )

                        # 其他异常也进行重试
                        if attempt < self.retry_count:
                            if progress_callback:
                                progress_callback(f"分段 {index + 1} 异常，{self.retry_interval}秒后重试...")
                            await asyncio.sleep(self.retry_interval)
                            continue
                        else:
                            return {
                                'index': index,
                                'success': False,
                                'error': error_msg,
                                'segment_text': segment_text[:100] + ('...' if len(segment_text) > 100 else '')
                            }

                # 不应该到达这里
                return {
                    'index': index,
                    'success': False,
                    'error': f"分段 {index + 1} 处理失败（未知原因）",
                    'segment_text': segment_text[:100] + ('...' if len(segment_text) > 100 else '')
                }

        # 创建所有任务
        tasks = [
            process_single_segment(i, segment)
            for i, segment in enumerate(segments)
        ]

        # 并发执行所有任务
        if progress_callback:
            progress_callback(f"开始异步处理 {len(tasks)} 个分段...")

        # 记录开始处理的日志
        logger.info(f"开始异步处理 {len(tasks)} 个分段，并发数: {min(self.thread_count, 10)}")

        try:
            results = await asyncio.gather(*tasks, return_exceptions=True)
        except Exception as e:
            logger.error(f"asyncio.gather 执行失败: {str(e)}", exception=e)
            raise

        # 处理结果
        for result in results:
            if result is None:  # 用户停止
                break
            if isinstance(result, Exception):
                failed_segments.append({
                    'index': -1,
                    'success': False,
                    'error': f"任务异常: {str(result)}",
                    'segment_text': ''
                })
            elif result['success']:
                segment_files.append(result)
            else:
                failed_segments.append(result)

        # 如果有失败的分段，报告错误
        if failed_segments:
            error_msg = f"有 {len(failed_segments)} 个分段处理失败"
            if progress_callback:
                progress_callback(error_msg)

        # 按索引排序
        segment_files.sort(key=lambda x: x['index'])

        # 合并音频文件
        if segment_files:
            return self._merge_segments(segment_files, file_name, output_dir, generate_srt, progress_callback, volume_raw)
        else:
            return {
                'success': False,
                'error': '所有分段处理失败'
            }

    async def _text_to_speech_async(self, text, voice, rate, pitch, volume, output_path, subtitle_path, progress_callback, segment_index, volume_raw=None):
        """异步文本转语音（带超时控制）"""
        try:
            # 使用超时控制，防止长时间卡住
            return await asyncio.wait_for(
                self._text_to_speech_async_impl(text, voice, rate, pitch, volume, output_path, subtitle_path, progress_callback, segment_index, volume_raw),
                timeout=120  # 120秒超时
            )
        except asyncio.TimeoutError:
            error_msg = f"分段 {segment_index + 1} 语音生成超时 (120秒)"
            if progress_callback:
                progress_callback(error_msg)
            logger.error(f"语音生成超时: {error_msg}")
            return False
        except Exception as e:
            error_msg = f"分段 {segment_index + 1} 语音生成失败: {str(e)}"
            if progress_callback:
                progress_callback(error_msg)

            # 记录详细错误日志
            logger.log_tts_error(
                text=text,
                voice=voice,
                rate=rate,
                pitch=pitch,
                volume=volume,
                error_message=f"异步语音生成失败: {str(e)}",
                exception=e
            )
            return False

    async def _text_to_speech_async_impl(self, text, voice, rate, pitch, volume, output_path, subtitle_path, progress_callback, segment_index, volume_raw=None):
        """异步文本转语音实现"""
        try:
            # 添加网络连接检查
            if progress_callback:
                progress_callback(f"分段 {segment_index + 1} 准备连接edge-tts服务...")

            # 构建SSML或直接使用文本
            if rate != 0 or pitch != 0:
                # 使用SSML格式
                rate_str = f"{rate:+d}%" if rate != 0 else "0%"
                pitch_str = f"{pitch:+d}Hz" if pitch != 0 else "0Hz"

                ssml_text = f'<speak><prosody rate="{rate_str}" pitch="{pitch_str}">{text}</prosody></speak>'
                communicate_kwargs = {
                    'text': ssml_text,
                    'voice': voice
                }
            else:
                # 直接使用文本
                communicate_kwargs = {
                    'text': text,
                    'voice': voice
                }

            # 创建Communicate对象
            if progress_callback:
                progress_callback(f"分段 {segment_index + 1} 创建TTS连接...")
            communicate = edge_tts.Communicate(**communicate_kwargs)

            # 异步保存（带超时控制）
            if subtitle_path:
                await asyncio.wait_for(
                    self._save_with_subtitles_async(communicate, output_path, subtitle_path, progress_callback, segment_index),
                    timeout=60  # 字幕处理60秒超时
                )
            else:
                await asyncio.wait_for(
                    communicate.save(output_path),
                    timeout=60  # 音频保存60秒超时
                )

            # 注意：音频后处理将在最终合并后统一进行，这里不做处理
            return True

        except asyncio.TimeoutError:
            error_msg = f"分段 {segment_index + 1} 保存操作超时"
            if progress_callback:
                progress_callback(error_msg)
            logger.error(error_msg)
            return False
        except Exception as e:
            error_msg = f"分段 {segment_index + 1} 语音生成失败: {str(e)}"
            if progress_callback:
                progress_callback(error_msg)

            # 记录详细错误日志
            logger.log_segment_error(
                segment_index=segment_index,
                segment_text=text[:200],  # 记录前200个字符
                error_message=f"异步语音生成实现失败: {str(e)}",
                exception=e
            )
            return False

    async def _save_with_subtitles_async(self, communicate, output_path, subtitle_path, progress_callback, segment_index):
        """异步保存音频和字幕（带超时控制）"""
        try:
            submaker = edge_tts.SubMaker()

            # 添加连接建立进度回调
            if progress_callback:
                progress_callback(f"分段 {segment_index + 1} 正在建立连接...")

            # 流式处理带超时控制
            with open(output_path, "wb") as audio_file:
                chunk_count = 0
                last_chunk_time = time.time()
                last_progress_time = time.time()
                connection_established = False

                # 使用asyncio.wait_for为整个流处理添加超时
                async def stream_with_timeout():
                    nonlocal chunk_count, last_chunk_time, last_progress_time, connection_established

                    async for chunk in communicate.stream():
                        current_time = time.time()

                        # 标记连接已建立
                        if not connection_established:
                            connection_established = True
                            if progress_callback:
                                progress_callback(f"分段 {segment_index + 1} 连接已建立，开始接收数据...")

                        # 检查是否超过30秒没有收到新数据块
                        if current_time - last_chunk_time > 30:
                            raise OperationTimeoutError(f"字幕流处理超时：30秒内未收到数据块")

                        if chunk["type"] == "audio":
                            audio_file.write(chunk["data"])
                            chunk_count += 1

                            # 增加进度更新频率，每100个音频块更新一次，确保超时监控有效
                            if chunk_count % 100 == 0 and progress_callback:
                                progress_callback(f"分段 {segment_index + 1} 正在处理音频数据... ({chunk_count} 块)")

                        elif chunk["type"] == "WordBoundary":
                            submaker.feed(chunk)

                        last_chunk_time = current_time

                        # 每5秒发送一次心跳进度，确保超时监控不会失效
                        if current_time - last_progress_time > 5:
                            if progress_callback:
                                progress_callback(f"分段 {segment_index + 1} 持续处理中... ({chunk_count} 音频块)")
                            last_progress_time = current_time

                # 为整个流处理添加90秒超时
                await asyncio.wait_for(stream_with_timeout(), timeout=90)

            # 保存字幕文件（分段处理时不进行优化，提高速度）
            if progress_callback:
                progress_callback(f"分段 {segment_index + 1} 正在生成字幕文件...")

            subtitle_content = submaker.generate_subs()
            with open(subtitle_path, "w", encoding="utf-8") as subtitle_file:
                subtitle_file.write(subtitle_content)

            if progress_callback:
                progress_callback(f"分段 {segment_index + 1} 字幕文件生成完成")

        except asyncio.TimeoutError:
            error_msg = f"分段 {segment_index + 1} 流处理超时 (90秒)"
            if progress_callback:
                progress_callback(error_msg)
            logger.error(f"字幕流处理超时: {error_msg}")
            raise OperationTimeoutError(error_msg)
        except OperationTimeoutError as e:
            if progress_callback:
                progress_callback(f"分段 {segment_index + 1} {str(e)}")
            logger.error(f"字幕处理超时: {str(e)}")
            raise
        except Exception as e:
            if progress_callback:
                progress_callback(f"分段 {segment_index + 1} 字幕生成失败: {str(e)}")
            raise

    def _merge_segments(self, segment_files, file_name, output_dir, generate_srt, progress_callback, volume_raw=None):
        """合并分段文件"""
        try:
            # 生成最终文件路径
            final_mp3 = os.path.join(output_dir, f"{file_name}.mp3")
            final_srt = os.path.join(output_dir, f"{file_name}.srt") if generate_srt else None

            # 提取音频和字幕文件路径
            audio_files = [sf['mp3_path'] for sf in segment_files]
            srt_files = [sf['srt_path'] for sf in segment_files if sf.get('srt_path')] if generate_srt else None

            # 合并音频文件
            success = self.merge_audio_files(
                audio_files, final_mp3, srt_files, final_srt, progress_callback
            )

            if success:
                # 应用最终的音频后处理
                if progress_callback:
                    progress_callback("正在进行最终音频后处理...")

                final_processing_success = self.apply_final_audio_processing(
                    final_mp3, volume_raw, progress_callback
                )

                if not final_processing_success:
                    if progress_callback:
                        progress_callback("音频后处理失败，但文件已生成")

                # 清理临时分段文件
                self._cleanup_segment_files(segment_files, progress_callback)

                return {
                    'success': True,
                    'mp3_path': final_mp3,
                    'srt_path': final_srt if generate_srt else None
                }
            else:
                return {
                    'success': False,
                    'error': '音频文件合并失败'
                }

        except Exception as e:
            return {
                'success': False,
                'error': f'合并分段失败: {str(e)}'
            }

    def _cleanup_segment_files(self, segment_files, progress_callback=None):
        """清理临时分段文件"""
        try:
            if progress_callback:
                progress_callback("清理临时文件...")

            for segment_file in segment_files:
                try:
                    if os.path.exists(segment_file['mp3_path']):
                        os.unlink(segment_file['mp3_path'])
                    if segment_file.get('srt_path') and os.path.exists(segment_file['srt_path']):
                        os.unlink(segment_file['srt_path'])
                except Exception as e:
                    if progress_callback:
                        progress_callback(f"清理临时文件失败: {str(e)}")
        except Exception:
            pass  # 清理失败不影响主流程

    def process_segments_threaded(self, segments, file_name, output_dir, voice, rate, pitch, volume, generate_srt, progress_callback, volume_raw=None):
        """
        多线程处理文本分段

        参数:
            segments (list): 文本分段列表
            file_name (str): 文件名（不含扩展名）
            output_dir (str): 输出目录
            voice (str): 语音名称
            rate (str): 语速
            pitch (str): 音调
            volume (str): 音量（SSML格式）
            generate_srt (bool): 是否生成字幕
            progress_callback (function): 进度回调函数
            volume_raw (int): 原始音量值（0-300），用于后处理
        """
        try:
            # 创建临时目录存放分段文件
            temp_dir = tempfile.mkdtemp(prefix=f"tts_{file_name}_")
            self._track_temp_dir(temp_dir)

            # 复制分段文件到输出目录以便查看
            segments_dir = os.path.join(output_dir, f"{file_name}_segments")
            os.makedirs(segments_dir, exist_ok=True)

            # 保存分段文本文件
            for i, segment in enumerate(segments):
                segment_file = os.path.join(segments_dir, f"{file_name}_{i+1}.txt")
                with open(segment_file, 'w', encoding='utf-8') as f:
                    f.write(segment)

            segment_files = []
            failed_segments = []

            def process_segment(segment_info):
                """处理单个分段"""
                index, segment_text = segment_info

                if self.should_stop:
                    return None

                # 检查暂停状态
                while self.is_paused and not self.should_stop:
                    time.sleep(1)

                if self.should_stop:
                    return None

                # 生成分段文件路径
                segment_mp3 = os.path.join(temp_dir, f"segment_{index:03d}.mp3")
                segment_srt = os.path.join(temp_dir, f"segment_{index:03d}.srt") if generate_srt else None

                # 更新进度
                if progress_callback:
                    progress_callback({
                        "type": "segment_progress",
                        "current_segment": index + 1,
                        "total_segments": len(segments),
                        "segment_text": f"正在处理第 {index + 1} 个分段"
                    })

                # 处理分段
                success = self.text_to_speech_with_retry(
                    segment_text, voice, rate, pitch, volume,
                    segment_mp3, segment_srt, progress_callback, index, volume_raw
                )

                if success:
                    return {
                        'index': index,
                        'mp3_path': segment_mp3,
                        'srt_path': segment_srt,
                        'success': True
                    }
                else:
                    error_msg = f"分段 {index + 1} 处理失败"
                    # 记录分段处理失败的详细信息
                    logger.log_segment_error(
                        segment_index=index,
                        segment_text=segment_text,
                        error_message="分段处理最终失败，所有重试都已用尽"
                    )
                    return {
                        'index': index,
                        'success': False,
                        'error': error_msg,
                        'segment_text': segment_text[:100] + ('...' if len(segment_text) > 100 else '')
                    }

            # 使用线程池处理分段
            with ThreadPoolExecutor(max_workers=self.thread_count) as executor:
                # 提交所有分段任务
                future_to_segment = {
                    executor.submit(process_segment, (i, segment)): i
                    for i, segment in enumerate(segments)
                }

                # 收集结果
                for future in as_completed(future_to_segment):
                    if self.should_stop:
                        break

                    result = future.result()
                    if result is None:  # 用户停止
                        break

                    if result['success']:
                        segment_files.append(result)
                    else:
                        failed_segments.append(result)
                        if progress_callback:
                            progress_callback(f"错误: {result['error']}")

            # 检查是否有失败的分段
            if failed_segments:
                failed_indices = [s['index']+1 for s in failed_segments]
                error_msg = f"以下分段处理失败: " + ", ".join([f"分段{idx}" for idx in failed_indices])

                # 记录失败分段的汇总信息
                logger.error(
                    f"分段处理失败汇总\n"
                    f"失败分段数量: {len(failed_segments)}\n"
                    f"总分段数量: {len(segments)}\n"
                    f"失败分段索引: {failed_indices}\n"
                    f"失败分段详情:\n" +
                    "\n".join([f"  分段{s['index']+1}: {s.get('segment_text', '未知文本')}" for s in failed_segments])
                )

                raise Exception(error_msg)

            if self.should_stop:
                raise Exception("用户停止处理")

            # 按索引排序分段文件
            segment_files.sort(key=lambda x: x['index'])

            # 合并音频文件
            final_mp3 = os.path.join(output_dir, f"{file_name}.mp3")
            final_srt = os.path.join(output_dir, f"{file_name}.srt") if generate_srt else None

            success = self.merge_audio_files(
                [sf['mp3_path'] for sf in segment_files],
                final_mp3,
                [sf['srt_path'] for sf in segment_files] if generate_srt else None,
                final_srt,
                progress_callback
            )

            if not success:
                raise Exception("音频合并失败")

            # 应用最终的音频后处理
            if progress_callback:
                progress_callback("正在进行音频后处理...")

            final_processing_success = self.apply_final_audio_processing(
                final_mp3, volume_raw, progress_callback
            )

            if not final_processing_success:
                if progress_callback:
                    progress_callback("音频后处理失败，但文件已生成")

            # 清理临时文件和分段文件夹
            try:
                # 清理临时目录
                shutil.rmtree(temp_dir)
                if progress_callback:
                    progress_callback("已清理临时文件")
            except Exception as e:
                if progress_callback:
                    progress_callback(f"清理临时文件失败: {str(e)}")

            try:
                # 清理分段文件夹
                if 'segments_dir' in locals() and os.path.exists(segments_dir):
                    shutil.rmtree(segments_dir)
                    if progress_callback:
                        progress_callback("已清理分段文件夹")
            except Exception as e:
                if progress_callback:
                    progress_callback(f"清理分段文件夹失败: {str(e)}")

            return {
                'success': True,
                'mp3_path': final_mp3,
                'srt_path': final_srt
            }

        except Exception as e:
            # 清理临时文件和分段文件夹
            try:
                if 'temp_dir' in locals() and os.path.exists(temp_dir):
                    shutil.rmtree(temp_dir)
            except:
                pass

            try:
                if 'segments_dir' in locals() and os.path.exists(segments_dir):
                    shutil.rmtree(segments_dir)
            except:
                pass

            return {
                'success': False,
                'error': str(e)
            }

    def merge_audio_files(self, audio_files, output_path, srt_files=None, output_srt=None, progress_callback=None):
        """
        合并多个音频文件（带超时控制）

        参数:
            audio_files (list): 音频文件路径列表
            output_path (str): 输出音频文件路径
            srt_files (list): 字幕文件路径列表（可选）
            output_srt (str): 输出字幕文件路径（可选）
            progress_callback (function): 进度回调函数
        """
        try:
            if progress_callback:
                progress_callback("开始合并音频文件...")

            # 确保FFmpeg路径设置正确（多线程环境下可能丢失）
            self.ensure_ffmpeg_path()

            # 计算超时时间：每个文件30秒，最少120秒
            timeout_seconds = max(120, len(audio_files) * 30)

            # 使用线程池执行合并操作，带超时控制
            with ThreadPoolExecutor(max_workers=1) as executor:
                if self.is_ffmpeg_available():
                    try:
                        # 提交FFmpeg合并任务
                        future = executor.submit(self._merge_audio_with_ffmpeg_timeout, audio_files, output_path, srt_files, output_srt, progress_callback)
                        result = future.result(timeout=timeout_seconds)

                        if progress_callback:
                            progress_callback("音频合并完成")
                        return result

                    except TimeoutError:
                        if progress_callback:
                            progress_callback(f"FFmpeg合并超时 ({timeout_seconds}秒)，使用pydub方案")
                        # 回退到pydub方案
                        future = executor.submit(self._merge_audio_with_pydub, audio_files, output_path, srt_files, output_srt, progress_callback)
                        return future.result(timeout=timeout_seconds)
                    except Exception as e:
                        if progress_callback:
                            progress_callback(f"FFmpeg合并失败，使用pydub: {str(e)}")
                        # 回退到pydub方案
                        future = executor.submit(self._merge_audio_with_pydub, audio_files, output_path, srt_files, output_srt, progress_callback)
                        return future.result(timeout=timeout_seconds)
                else:
                    # 使用pydub合并音频
                    future = executor.submit(self._merge_audio_with_pydub, audio_files, output_path, srt_files, output_srt, progress_callback)
                    return future.result(timeout=timeout_seconds)

        except TimeoutError:
            error_msg = f"音频合并超时 ({timeout_seconds}秒)"
            if progress_callback:
                progress_callback(error_msg)
            logger.error(f"音频合并超时: {error_msg}")
            return False
        except Exception as e:
            error_msg = f"音频合并失败: {str(e)}"
            if progress_callback:
                progress_callback(error_msg)

            # 记录详细的音频合并错误
            logger.log_audio_processing_error(
                operation="音频合并",
                input_file=f"{len(audio_files)}个分段文件",
                output_file=output_path,
                error_message=f"合并失败: {str(e)}",
                exception=e
            )

            # 记录输入文件列表
            logger.error(
                f"音频合并失败详情\n"
                f"输入文件数量: {len(audio_files)}\n"
                f"输出文件: {output_path}\n"
                f"输入文件列表:\n" +
                "\n".join([f"  {i+1}: {f}" for i, f in enumerate(audio_files)])
            )
            return False

    def _merge_audio_with_ffmpeg_timeout(self, audio_files, output_path, srt_files, output_srt, progress_callback):
        """FFmpeg合并音频（带超时控制的版本）"""
        try:
            # 执行FFmpeg合并
            self._merge_audio_with_ffmpeg(audio_files, output_path, progress_callback)

            # 处理字幕合并
            if output_srt and srt_files:
                self._merge_srt_files_for_audio(srt_files, output_srt, audio_files, progress_callback)

                # 合并完成后统一优化字幕格式
                if progress_callback:
                    progress_callback("正在优化字幕格式...")
                self._optimize_merged_subtitle_file(output_srt)

                # 基于最终音频文件校准字幕时间戳，消除累积误差
                self._calibrate_final_subtitle_timing(output_srt, output_path, progress_callback)

            return True
        except Exception as e:
            logger.error(f"FFmpeg合并失败: {str(e)}")
            raise

    def _merge_audio_with_pydub(self, audio_files, output_path, srt_files=None, output_srt=None, progress_callback=None):
        """使用pydub合并音频文件（备用方案）"""
        try:
            combined = AudioSegment.empty()
            current_time = 0
            srt_entries = []

            for i, audio_file in enumerate(audio_files):
                if self.should_stop:
                    return False

                if progress_callback:
                    progress_callback(f"合并音频分段 {i+1}/{len(audio_files)}")

                # 加载音频分段
                segment = AudioSegment.from_mp3(audio_file)
                combined += segment

                # 处理字幕
                if srt_files and i < len(srt_files) and srt_files[i] and os.path.exists(srt_files[i]):
                    try:
                        srt_content = self._read_file_with_encoding_detection(srt_files[i])

                        # 解析SRT并调整时间戳
                        segment_entries = self.parse_srt(srt_content)
                        for entry in segment_entries:
                            # 调整时间戳
                            entry['start'] += current_time / 1000.0  # 转换为秒
                            entry['end'] += current_time / 1000.0
                            srt_entries.append(entry)
                    except Exception as e:
                        if progress_callback:
                            progress_callback(f"处理字幕文件 {i+1} 时出错: {str(e)}")

                current_time += len(segment)

            # 导出合并后的音频
            if progress_callback:
                progress_callback("导出合并后的音频文件...")

            combined.export(output_path, format="mp3")

            # 生成合并后的字幕文件
            if output_srt and srt_entries:
                if progress_callback:
                    progress_callback("生成合并后的字幕文件...")

                self.write_srt(srt_entries, output_srt)

                # 优化字幕格式
                if progress_callback:
                    progress_callback("正在优化字幕格式...")
                self._optimize_merged_subtitle_file(output_srt)

                # 基于最终音频文件校准字幕时间戳，消除累积误差
                self._calibrate_final_subtitle_timing(output_srt, output_path, progress_callback)

            if progress_callback:
                progress_callback("pydub音频合并完成")

            return True

        except Exception as e:
            if progress_callback:
                progress_callback(f"pydub合并失败: {str(e)}")
            return False

    def _merge_srt_files_for_audio(self, srt_files, output_srt, audio_files, progress_callback=None):
        """为音频合并处理字幕文件，基于实际音频时长精确校准时间戳"""
        try:
            if progress_callback:
                progress_callback("正在处理字幕文件...")

            srt_entries = []

            # 第一步：收集所有分段的字幕和音频时长信息
            segment_info = []

            for i, srt_file in enumerate(srt_files):
                # 获取当前音频分段的实际时长
                audio_duration = 0.0
                if i < len(audio_files) and os.path.exists(audio_files[i]):
                    try:
                        # 使用FFprobe获取精确的音频时长
                        audio_duration = self._get_audio_duration_ffprobe(audio_files[i])
                        if audio_duration <= 0:
                            # 如果FFprobe失败，使用pydub作为备用
                            from pydub import AudioSegment
                            audio = AudioSegment.from_mp3(audio_files[i])
                            audio_duration = len(audio) / 1000.0  # 转换为秒
                    except Exception:
                        audio_duration = 5.0  # 默认5秒

                # 解析字幕文件
                segment_entries = []
                subtitle_duration = 0.0

                if srt_file and os.path.exists(srt_file):
                    try:
                        srt_content = self._read_file_with_encoding_detection(srt_file)
                        segment_entries = self.parse_srt(srt_content)
                        if segment_entries:
                            subtitle_duration = segment_entries[-1]['end']
                    except Exception as e:
                        if progress_callback:
                            progress_callback(f"处理字幕文件 {i+1} 时出错: {str(e)}")

                segment_info.append({
                    'audio_duration': audio_duration,
                    'subtitle_duration': subtitle_duration,
                    'entries': segment_entries,
                    'index': i
                })

            # 第二步：基于实际音频时长重新校准所有字幕时间戳
            current_audio_time = 0.0

            for segment in segment_info:
                audio_duration = segment['audio_duration']
                subtitle_duration = segment['subtitle_duration']
                entries = segment['entries']

                if not entries:
                    # 没有字幕条目，直接跳过
                    current_audio_time += audio_duration
                    continue

                # 计算时间缩放比例（音频实际时长 / 字幕时长）
                if subtitle_duration > 0:
                    time_scale = audio_duration / subtitle_duration
                else:
                    time_scale = 1.0

                # 应用时间偏移和缩放
                for entry in entries:
                    # 先缩放时间戳以匹配实际音频时长
                    scaled_start = entry['start'] * time_scale
                    scaled_end = entry['end'] * time_scale

                    # 再添加累积的时间偏移
                    entry['start'] = current_audio_time + scaled_start
                    entry['end'] = current_audio_time + scaled_end

                    srt_entries.append(entry)

                # 使用实际音频时长更新偏移
                current_audio_time += audio_duration

            # 生成合并后的字幕文件
            if srt_entries:
                if progress_callback:
                    progress_callback("生成合并后的字幕文件...")

                self.write_srt(srt_entries, output_srt)

                # 优化字幕格式
                if progress_callback:
                    progress_callback("正在优化字幕格式...")
                self._optimize_merged_subtitle_file(output_srt)

        except Exception as e:
            if progress_callback:
                progress_callback(f"字幕处理失败: {str(e)}")

    def _get_audio_duration_ffprobe(self, audio_file):
        """使用FFprobe获取音频文件的精确时长"""
        try:
            # 获取FFmpeg路径
            ffmpeg_path = config_manager.get_ffmpeg_path()
            ffprobe_exe = None

            if ffmpeg_path:
                possible_paths = [
                    os.path.join(ffmpeg_path, 'ffprobe.exe'),
                    os.path.join(ffmpeg_path, 'bin', 'ffprobe.exe'),
                    ffmpeg_path if ffmpeg_path.endswith('ffprobe.exe') else None
                ]

                for path in possible_paths:
                    if path and os.path.exists(path):
                        ffprobe_exe = path
                        break

            if not ffprobe_exe:
                ffprobe_exe = 'ffprobe'

            # 使用FFprobe获取音频时长
            cmd = [
                ffprobe_exe,
                '-v', 'quiet',
                '-show_entries', 'format=duration',
                '-of', 'csv=p=0',
                audio_file
            ]

            result = subprocess.run(cmd, capture_output=True, text=True, timeout=10)
            if result.returncode == 0 and result.stdout.strip():
                duration = float(result.stdout.strip())
                return duration
            else:
                return 0.0

        except Exception:
            return 0.0

    def _calibrate_final_subtitle_timing(self, srt_file, audio_file, progress_callback=None):
        """基于最终音频文件校准字幕时间戳，消除累积误差"""
        try:
            if not os.path.exists(srt_file) or not os.path.exists(audio_file):
                return False

            if progress_callback:
                progress_callback("正在校准字幕时间戳...")

            # 获取最终音频文件的实际总时长
            final_audio_duration = self._get_audio_duration_ffprobe(audio_file)
            if final_audio_duration <= 0:
                from pydub import AudioSegment
                audio = AudioSegment.from_mp3(audio_file)
                final_audio_duration = len(audio) / 1000.0

            # 读取字幕文件
            srt_content = self._read_file_with_encoding_detection(srt_file)

            entries = self.parse_srt(srt_content)
            if not entries:
                return False

            # 获取字幕的总时长
            subtitle_total_duration = entries[-1]['end']

            # 计算时间校准比例
            if subtitle_total_duration > 0:
                calibration_ratio = final_audio_duration / subtitle_total_duration

                # 如果比例接近1.0（误差小于0.1%），则不需要校准
                if abs(calibration_ratio - 1.0) < 0.001:
                    if progress_callback:
                        progress_callback("字幕时间戳已经很准确，无需校准")
                    return True

                if progress_callback:
                    progress_callback(f"检测到时间偏差，校准比例: {calibration_ratio:.6f}")

                # 应用校准比例到所有字幕条目
                for entry in entries:
                    entry['start'] *= calibration_ratio
                    entry['end'] *= calibration_ratio

                # 写回校准后的字幕文件
                self.write_srt(entries, srt_file)

                if progress_callback:
                    progress_callback("字幕时间戳校准完成")

                return True
            else:
                return False

        except Exception as e:
            if progress_callback:
                progress_callback(f"字幕校准失败: {str(e)}")
            return False

    def parse_srt(self, srt_content):
        """
        解析SRT字幕内容

        参数:
            srt_content (str): SRT文件内容

        返回:
            list: 字幕条目列表
        """
        entries = []
        blocks = srt_content.strip().split('\n\n')

        for block in blocks:
            lines = block.strip().split('\n')
            if len(lines) >= 3:
                try:
                    # 解析时间戳
                    time_line = lines[1]
                    start_str, end_str = time_line.split(' --> ')
                    start_time = self._srt_time_to_seconds(start_str)
                    end_time = self._srt_time_to_seconds(end_str)

                    # 获取文本并格式化
                    text = '\n'.join(lines[2:])
                    formatted_text = self._format_subtitle_text(text)

                    entries.append({
                        'start': start_time,
                        'end': end_time,
                        'text': formatted_text
                    })
                except:
                    continue  # 跳过无效的条目

        return entries

    def _format_subtitle_text(self, text):
        """格式化字幕文本，去除多余空格和优化显示"""
        if not text:
            return text

        try:
            # 1. 去除首尾空白
            formatted_text = text.strip()

            # 2. 将多个连续空格替换为单个空格
            import re
            formatted_text = re.sub(r'\s+', ' ', formatted_text)

            # 3. 去除中文字符之间的不必要空格（多次处理确保彻底）
            # 匹配中文字符之间的空格，需要多次处理
            for _ in range(3):  # 多次处理确保彻底去除
                formatted_text = re.sub(r'([\u4e00-\u9fff])\s+([\u4e00-\u9fff])', r'\1\2', formatted_text)

            # 4. 去除中文字符与标点符号之间的空格
            formatted_text = re.sub(r'([\u4e00-\u9fff])\s+([，。！？；：、])', r'\1\2', formatted_text)
            formatted_text = re.sub(r'([，。！？；：、])\s+([\u4e00-\u9fff])', r'\1\2', formatted_text)

            # 5. 处理中文与英文之间的空格
            # 中文与英文之间保留一个空格，但去除多余空格
            formatted_text = re.sub(r'([\u4e00-\u9fff])\s+([a-zA-Z])', r'\1 \2', formatted_text)
            formatted_text = re.sub(r'([a-zA-Z])\s+([\u4e00-\u9fff])', r'\1 \2', formatted_text)

            # 6. 再次去除中文字符之间可能残留的空格
            formatted_text = re.sub(r'([\u4e00-\u9fff])\s+([\u4e00-\u9fff])', r'\1\2', formatted_text)

            # 7. 处理换行符周围的空格
            formatted_text = re.sub(r'\s*\n\s*', '\n', formatted_text)

            # 8. 最终清理：确保没有行首行尾空格
            lines = formatted_text.split('\n')
            formatted_lines = [line.strip() for line in lines if line.strip()]
            formatted_text = '\n'.join(formatted_lines)

            return formatted_text

        except Exception as e:
            print(f"格式化字幕文本失败: {e}")
            # 如果格式化失败，至少去除首尾空白和多余空格
            return ' '.join(text.split())

    def write_srt(self, entries, output_path):
        """
        写入SRT字幕文件

        参数:
            entries (list): 字幕条目列表
            output_path (str): 输出文件路径
        """
        with open(output_path, 'w', encoding='utf-8') as f:
            for i, entry in enumerate(entries, 1):
                start_str = self._seconds_to_srt_time(entry['start'])
                end_str = self._seconds_to_srt_time(entry['end'])

                f.write(f"{i}\n")
                f.write(f"{start_str} --> {end_str}\n")
                f.write(f"{entry['text']}\n\n")
    
    def process_files(self, file_paths, output_dir, voice, rate, pitch, volume,
                     generate_srt=True, progress_callback=None, volume_raw=None):
        """
        批量处理多个文件

        参数:
            file_paths (list): 文件路径列表
            output_dir (str): 输出目录
            voice (str): 语音名称
            rate (str): 语速
            pitch (str): 音调
            volume (str): 音量（SSML格式）
            generate_srt (bool): 是否生成字幕
            progress_callback (function): 进度回调函数
            volume_raw (int): 原始音量值（0-300），用于后处理
        """
        results = []

        # 重置控制标志
        self.should_stop = False
        self.is_paused = False

        # 处理文件
        for i in range(len(file_paths)):
            # 检查停止标志
            if self.should_stop:
                if progress_callback:
                    progress_callback("用户停止了处理")
                break

            # 检查暂停标志
            while self.is_paused and not self.should_stop:
                if progress_callback:
                    progress_callback("处理已暂停...")
                time.sleep(1)  # 暂停时等待1秒后再检查

            # 再次检查停止标志（暂停期间可能被停止）
            if self.should_stop:
                if progress_callback:
                    progress_callback("用户停止了处理")
                break

            file_path = file_paths[i]

            if progress_callback:
                progress_callback({
                    "type": "file_progress",
                    "current_file": i + 1,
                    "total_files": len(file_paths),
                    "file_name": os.path.basename(file_path),
                    "message": f"正在处理文件 {i+1}/{len(file_paths)}: {os.path.basename(file_path)}"
                })

            # 处理单个文件
            result = self.process_file(
                file_path, output_dir, voice, rate, pitch, volume,
                generate_srt, progress_callback, volume_raw
            )
            result['file_path'] = file_path
            result['file_index'] = i
            results.append(result)

            # 如果处理失败且包含分段错误信息，记录详细信息
            if not result['success'] and '分段' in result.get('error', ''):
                if progress_callback:
                    progress_callback(f"文件 {os.path.basename(file_path)} 处理失败: {result['error']}")



            if progress_callback:
                progress_callback({
                    "type": "overall_progress",
                    "completed": i + 1,
                    "total": len(file_paths),
                    "percentage": ((i + 1) / len(file_paths)) * 100
                })



        return results

    def _track_temp_file(self, file_path):
        """跟踪临时文件"""
        with self._temp_lock:
            self.temp_files.add(file_path)

    def _track_temp_dir(self, dir_path):
        """跟踪临时目录"""
        with self._temp_lock:
            self.temp_dirs.add(dir_path)

    def _untrack_temp_file(self, file_path):
        """取消跟踪临时文件"""
        with self._temp_lock:
            self.temp_files.discard(file_path)

    def _untrack_temp_dir(self, dir_path):
        """取消跟踪临时目录"""
        with self._temp_lock:
            self.temp_dirs.discard(dir_path)

    def cleanup_temp_files(self):
        """清理所有跟踪的临时文件和目录"""
        cleaned_files = 0
        cleaned_dirs = 0

        # 清理临时文件
        with self._temp_lock:
            temp_files_copy = self.temp_files.copy()
            temp_dirs_copy = self.temp_dirs.copy()

        # 清理文件
        for file_path in temp_files_copy:
            try:
                if os.path.exists(file_path):
                    os.unlink(file_path)
                    cleaned_files += 1
                self._untrack_temp_file(file_path)
            except Exception as e:
                print(f"清理临时文件失败: {file_path}, 错误: {e}")

        # 清理目录
        for dir_path in temp_dirs_copy:
            try:
                if os.path.exists(dir_path):
                    shutil.rmtree(dir_path)
                    cleaned_dirs += 1
                self._untrack_temp_dir(dir_path)
            except Exception as e:
                print(f"清理临时目录失败: {dir_path}, 错误: {e}")

        if cleaned_files > 0 or cleaned_dirs > 0:
            print(f"AI配音缓存清理完成: 清理了 {cleaned_files} 个临时文件, {cleaned_dirs} 个临时目录")
        else:
            print("AI配音缓存清理完成: 没有发现需要清理的临时文件")

    def stop_processing(self):
        """停止当前处理"""
        self.should_stop = True
        self.is_paused = False  # 停止时也取消暂停状态

        # 终止所有FFmpeg进程
        print("正在终止所有FFmpeg进程...")
        ffmpeg_manager.kill_all_ffmpeg_processes()

        # 清理缓存文件
        print("正在清理AI配音缓存文件...")
        self.cleanup_temp_files()

    def cleanup_on_exit(self):
        """退出时清理资源"""
        print("清理AI配音资源...")
        self.stop_processing()
        ffmpeg_manager.cleanup_zombie_processes()

    def update_settings_from_config(self):
        """从配置更新设置"""
        self.max_segment_length = config_manager.get_segment_length()
        self.thread_count = config_manager.get_thread_count()
        self.retry_count = config_manager.get_retry_count()
        self.retry_interval = config_manager.get_retry_interval()
        # 停顿移除设置已移除
        self.enhance_quality = config_manager.get_enhance_quality()
        self.enhance_clarity = config_manager.get_enhance_clarity()
        self.normalize_volume = config_manager.get_normalize_volume()

    def _read_file_with_encoding_detection(self, file_path):
        """
        智能读取文件，使用编码检测

        参数:
            file_path (str): 文件路径

        返回:
            str: 文件内容
        """
        try:
            # 首先尝试使用chardet检测编码
            encoding_info = self._detect_file_encoding(file_path)
            detected_encoding = encoding_info['encoding']
            confidence = encoding_info['confidence']

            if detected_encoding and confidence > 0.7:
                # 如果检测到的编码置信度较高，使用检测到的编码
                try:
                    with open(file_path, 'r', encoding=detected_encoding) as f:
                        content = f.read()
                    return content

                except Exception as e:
                    print(f"使用检测到的编码 {detected_encoding} 读取失败: {e}")

            # 如果chardet检测失败或置信度低，尝试常用编码
            encodings = ['utf-8', 'gbk', 'gb2312', 'gb18030', 'utf-16', 'latin-1', 'cp1252']

            for encoding in encodings:
                try:
                    with open(file_path, 'r', encoding=encoding) as f:
                        content = f.read()

                    # 简单验证内容是否正常（检查是否有过多的替换字符）
                    if self._validate_content(content):
                        return content

                except Exception:
                    continue

            # 如果所有编码都失败，使用二进制模式读取
            with open(file_path, 'rb') as f:
                raw_data = f.read()

            # 尝试用utf-8解码，替换无法解码的字符
            content = raw_data.decode('utf-8', errors='replace')
            return content

        except Exception as e:
            raise Exception(f"无法读取文件: {str(e)}")

    def _detect_file_encoding(self, file_path):
        """
        使用chardet检测文件编码

        参数:
            file_path (str): 文件路径

        返回:
            dict: 包含编码信息的字典
        """
        try:
            # 尝试导入chardet
            import chardet
        except ImportError:
            # 如果没有安装chardet，返回默认值
            return {'encoding': None, 'confidence': 0}

        try:
            # 读取文件的一部分来检测编码
            with open(file_path, 'rb') as f:
                # 读取前10KB来检测编码，对于大文件这样更高效
                raw_data = f.read(10240)
                if len(raw_data) < 10240:
                    # 如果文件小于10KB，读取全部
                    f.seek(0)
                    raw_data = f.read()

            # 使用chardet检测编码
            result = chardet.detect(raw_data)
            return result

        except Exception as e:
            print(f"编码检测失败: {e}")
            return {'encoding': None, 'confidence': 0}

    def _validate_content(self, content):
        """
        验证文件内容是否正常

        参数:
            content (str): 文件内容

        返回:
            bool: 内容是否正常
        """
        if not content:
            return True

        # 检查替换字符的比例
        replacement_chars = content.count('�')
        total_chars = len(content)

        if total_chars == 0:
            return True

        # 如果替换字符超过5%，认为编码可能不正确
        replacement_ratio = replacement_chars / total_chars
        return replacement_ratio < 0.05
